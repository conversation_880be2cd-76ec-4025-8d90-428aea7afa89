/**
 * SMTP Tester Layout
 *
 * Layout component for SMTP Tester tool with breadcrumbs and SEO
 */

import React from 'react';
import Breadcrumbs from '@/components/seo/Breadcrumbs';
import { WebApplicationSchema, BreadcrumbSchema } from '@/components/seo/StructuredData';

interface SmtpTesterLayoutProps {
  children: React.ReactNode;
}

export default function SmtpTesterLayout({ children }: SmtpTesterLayoutProps) {
  const breadcrumbItems = [
    { name: 'Home', url: '/' },
    { name: 'Tools', url: '/tools' },
    { name: 'SMTP Tester', url: '/tools/smtp-tester' }
  ];

  const webAppProps = {
    name: 'SMTP Tester Tool',
    description: 'Test SMTP server connectivity and email authentication protocols (SPF, DKIM, DMARC) to ensure optimal email deliverability.',
    url: 'https://vanishpost.com/tools/smtp-tester',
    applicationCategory: 'EmailTool',
    operatingSystem: 'Web Browser'
  };

  return (
    <>
      <WebApplicationSchema {...webAppProps} />
      <BreadcrumbSchema items={breadcrumbItems} />

      <div className="min-h-screen" style={{ backgroundColor: '#fbfaf8' }}>
        {/* Breadcrumbs */}
        <div className="bg-white/50 backdrop-blur-sm border-b border-[#4a3728]/10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <Breadcrumbs items={breadcrumbItems} />
          </div>
        </div>

        {/* Main Content */}
        {children}
      </div>
    </>
  );
}
