/**
 * SMTP Tester Tool Page
 *
 * Main page for testing SMTP server connectivity and email authentication
 */

'use client';

import React, { useEffect } from 'react';
import { useSmtpTester } from '@/hooks/useSmtpTester';
import SmtpTesterForm from '@/components/tools/smtp-tester/SmtpTesterForm';
import SmtpResults from '@/components/tools/smtp-tester/SmtpResults';
import SmtpLoadingOverlay from '@/components/tools/smtp-tester/SmtpLoadingOverlay';
import SmtpInstructions from '@/components/tools/smtp-tester/SmtpInstructions';

export default function SmtpTesterPage() {
  const {
    formData,
    validationErrors,
    isLoading,
    result,
    error,
    validationSuccess,
    updateFormData,
    testSmtpConnection,
    validateSmtpConfig,
    clearError,
    reset
  } = useSmtpTester();

  // Auto-scroll to results when they become available
  useEffect(() => {
    if (result && !isLoading) {
      const resultsElement = document.getElementById('smtp-results');
      if (resultsElement) {
        resultsElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }
  }, [result, isLoading]);

  const handleFormSubmit = async (formData: any) => {
    clearError();
    await testSmtpConnection(formData);
  };

  const handleValidation = async (formData: any) => {
    clearError();
    await validateSmtpConfig(formData);
  };

  const handleNewTest = () => {
    reset();
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen" style={{ backgroundColor: '#fbfaf8' }}>
      {/* Loading Overlay */}
      <SmtpLoadingOverlay
        isVisible={isLoading}
        testMode={formData.testMode}
        message={formData.testMode === 'auto' ? 'Testing SMTP and analyzing authentication...' : 'Testing SMTP connectivity...'}
      />

      {/* Hero Section */}
      <div className="bg-[#fbfaf8]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-[#66b077] to-[#07880e] flex items-center justify-center shadow-lg">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-4" style={{ color: '#1b130e' }}>
              SMTP Tester Tool
            </h1>
            <p className="text-xl md:text-2xl mb-6" style={{ color: '#4a3728' }}>
              Test SMTP server connectivity and email authentication
            </p>
            <p className="text-lg max-w-3xl mx-auto" style={{ color: '#956b50' }}>
              Verify your SMTP configuration, test email delivery, and analyze 5-protocol authentication
              (SPF, DKIM, DMARC, MX Records, and Reverse DNS) to ensure optimal email deliverability.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Unified SMTP Configuration and Mode Comparison Card */}
          <SmtpTesterForm
            formData={formData}
            validationErrors={validationErrors}
            isLoading={isLoading}
            validationSuccess={validationSuccess}
            onFormDataChange={updateFormData}
            onSubmit={handleFormSubmit}
            onValidate={handleValidation}
          />

          {/* Error Display */}
          {error && (
            <div className="bg-white/80 backdrop-blur-sm rounded-lg border border-red-500/30 p-4">
              <div className="flex items-start gap-3">
                <svg className="w-6 h-6 text-red-500 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="flex-1">
                  <h3 className="font-semibold text-red-700 mb-1">Test Failed</h3>
                  <p className="text-red-600 text-sm">{error}</p>
                  <button
                    onClick={clearError}
                    className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
                  >
                    Dismiss
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Results Section */}
          {result && (
            <div id="smtp-results">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-2xl font-bold" style={{ color: '#1b130e' }}>Test Results</h2>
                <button
                  onClick={handleNewTest}
                  className="px-4 py-2 border border-[#4a3728] text-[#4a3728] rounded-md hover:bg-[#4a3728] hover:text-white transition-colors duration-200"
                >
                  New Test
                </button>
              </div>
              <SmtpResults result={result} testMode={formData.testMode} />
            </div>
          )}

          {/* Instructions Section */}
          <SmtpInstructions />
        </div>
      </div>

      {/* About SMTP Testing Section */}
      <div className="bg-[#fbfaf8] mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="bg-white/80 backdrop-blur-sm rounded-lg border border-[#4a3728]/20 p-6">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-[#f59e0b] to-[#ce601c] flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h2 className="text-2xl font-bold" style={{ color: '#1b130e' }}>About SMTP Testing</h2>
                <p className="text-sm" style={{ color: '#4a3728' }}>Understanding SMTP connectivity and email authentication</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold mb-3" style={{ color: '#1b130e' }}>What is SMTP Testing?</h3>
                <p className="text-sm mb-4" style={{ color: '#4a3728' }}>
                  SMTP (Simple Mail Transfer Protocol) testing verifies that your email server configuration
                  is working correctly and can successfully send emails. This includes testing server
                  connectivity, authentication, and delivery capabilities.
                </p>

                <h4 className="font-medium mb-2" style={{ color: '#1b130e' }}>Key Benefits:</h4>
                <ul className="space-y-1 text-sm" style={{ color: '#4a3728' }}>
                  <li className="flex items-start gap-2">
                    <svg className="w-4 h-4 mt-0.5 flex-shrink-0" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Verify SMTP server connectivity
                  </li>
                  <li className="flex items-start gap-2">
                    <svg className="w-4 h-4 mt-0.5 flex-shrink-0" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Test authentication credentials
                  </li>
                  <li className="flex items-start gap-2">
                    <svg className="w-4 h-4 mt-0.5 flex-shrink-0" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Validate email delivery
                  </li>
                  <li className="flex items-start gap-2">
                    <svg className="w-4 h-4 mt-0.5 flex-shrink-0" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Troubleshoot configuration issues
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-3" style={{ color: '#1b130e' }}>Email Authentication</h3>
                <p className="text-sm mb-4" style={{ color: '#4a3728' }}>
                  Our auto mode analyzes 5 comprehensive email authentication protocols to ensure your emails
                  are properly authenticated and won't be marked as spam.
                </p>

                <h4 className="font-medium mb-2" style={{ color: '#1b130e' }}>5-Protocol Authentication Analysis:</h4>
                <ul className="space-y-1 text-sm" style={{ color: '#4a3728' }}>
                  <li className="flex items-start gap-2">
                    <svg className="w-4 h-4 mt-0.5 flex-shrink-0" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                    <div>
                      <strong>SPF</strong> - Sender Policy Framework validation
                    </div>
                  </li>
                  <li className="flex items-start gap-2">
                    <svg className="w-4 h-4 mt-0.5 flex-shrink-0" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                    <div>
                      <strong>DKIM</strong> - DomainKeys Identified Mail signatures
                    </div>
                  </li>
                  <li className="flex items-start gap-2">
                    <svg className="w-4 h-4 mt-0.5 flex-shrink-0" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                    <div>
                      <strong>DMARC</strong> - Domain-based Message Authentication
                    </div>
                  </li>
                  <li className="flex items-start gap-2">
                    <svg className="w-4 h-4 mt-0.5 flex-shrink-0" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                    <div>
                      <strong>MX Records</strong> - Mail exchange server validation
                    </div>
                  </li>
                  <li className="flex items-start gap-2">
                    <svg className="w-4 h-4 mt-0.5 flex-shrink-0" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                    <div>
                      <strong>Reverse DNS</strong> - IP address hostname verification
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
