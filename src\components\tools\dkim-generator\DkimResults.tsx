'use client';

// DKIM Results Display Component - Enhanced with modern UI and collapsible instructions
import React, { useState } from 'react';
import { DkimGenerateResponse } from '@/types/dkim';
import DnsRecordDisplay from '../shared/DnsRecordDisplay';
import CopyableField from '../shared/CopyableField';

interface DkimResultsProps {
  result?: DkimGenerateResponse['data'];
  loading?: boolean;
  onValidate?: (domain: string, selector: string, publicKey: string) => void;
  validating?: boolean;
}

// Collapsible Section Component
interface CollapsibleSectionProps {
  title: string;
  icon: React.ReactNode;
  children: React.ReactNode;
  defaultOpen?: boolean;
  variant?: 'primary' | 'secondary';
}

function CollapsibleSection({ title, icon, children, defaultOpen = false, variant = 'primary' }: CollapsibleSectionProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  const bgColor = variant === 'primary' ? 'bg-[#66b077]/5' : 'bg-[#f3ece8]';
  const borderColor = variant === 'primary' ? 'border-[#66b077]/20' : 'border-[#4a3728]/20';
  const iconColor = variant === 'primary' ? 'text-[#66b077]' : 'text-[#956b50]';

  return (
    <div className={`${bgColor} ${borderColor} border rounded-xl overflow-hidden transition-all duration-200`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-6 py-4 flex items-center justify-between hover:bg-black/5 transition-colors"
      >
        <div className="flex items-center space-x-3">
          <div className={`${iconColor} flex-shrink-0`}>
            {icon}
          </div>
          <h3 className="text-lg font-semibold text-[#1b130e] text-left">
            {title}
          </h3>
        </div>
        <div className={`transform transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}>
          <svg className="w-5 h-5 text-[#4a3728]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </button>

      <div className={`transition-all duration-300 ease-in-out ${isOpen ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0'} overflow-hidden`}>
        <div className="px-6 pb-6">
          {children}
        </div>
      </div>
    </div>
  );
}

export default function DkimResults({ result, loading, onValidate, validating }: DkimResultsProps) {

  if (loading) {
    return (
      <div className="bg-white border border-[#4a3728] rounded-lg p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-[#f3ece8] rounded w-1/3 mb-4"></div>
          <div className="space-y-4">
            <div>
              <div className="h-4 bg-[#f3ece8] rounded w-1/4 mb-2"></div>
              <div className="h-20 bg-[#f3ece8] rounded"></div>
            </div>
            <div>
              <div className="h-4 bg-[#f3ece8] rounded w-1/4 mb-2"></div>
              <div className="h-32 bg-[#f3ece8] rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!result) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Modern Success Header */}
      <div className="bg-gradient-to-r from-[#66b077]/10 to-[#07880e]/10 border border-[#66b077]/30 rounded-2xl p-4">
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-gradient-to-br from-[#66b077] to-[#07880e] rounded-xl flex items-center justify-center shadow-lg">
              <svg className="h-6 w-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
          <div className="flex-1">
            <h3 className="text-xl font-bold text-[#1b130e] mb-2">
              DKIM Keys Generated Successfully!
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <div className="bg-white/60 backdrop-blur-sm rounded-lg p-2 border border-[#4a3728]/10">
                <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">Domain</div>
                <div className="font-mono font-semibold text-[#1b130e] text-sm">{result.domain}</div>
              </div>
              <div className="bg-white/60 backdrop-blur-sm rounded-lg p-2 border border-[#4a3728]/10">
                <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">Selector</div>
                <div className="font-mono font-semibold text-[#1b130e] text-sm">{result.selector}</div>
              </div>
              <div className="bg-white/60 backdrop-blur-sm rounded-lg p-2 border border-[#4a3728]/10">
                <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">Key Strength</div>
                <div className="font-semibold text-[#1b130e]">{result.keyStrength} bits</div>
              </div>
            </div>
            <div className="mt-4 text-sm text-[#4a3728]">
              Generated on {new Date(result.generatedAt).toLocaleString()} • Expires {new Date(result.expiresAt).toLocaleDateString()}
            </div>
          </div>
        </div>
      </div>

      {/* DNS Record Display - Enhanced */}
      <div className="bg-white/80 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-4 shadow-sm">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-8 h-8 bg-[#66b077]/10 rounded-lg flex items-center justify-center">
            <svg className="w-4 h-4 text-[#66b077]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-bold text-[#1b130e]">DNS TXT Record</h3>
        </div>
        <DnsRecordDisplay
          recordName={result.recordName}
          recordValue={result.dnsRecord}
          recordType="TXT"
          title=""
        />
      </div>

      {/* Private Key Display - Enhanced */}
      <div className="bg-white/80 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-8 shadow-sm">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-[#f59e0b]/10 rounded-lg flex items-center justify-center">
            <svg className="w-5 h-5 text-[#f59e0b]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
            </svg>
          </div>
          <h3 className="text-xl font-bold text-[#1b130e]">Private Key</h3>
        </div>

        <CopyableField
          label=""
          value={result.privateKey}
          multiline={true}
          sensitive={true}
        />


      </div>

      {/* Quick Setup Guide - Collapsible */}
      <CollapsibleSection
        title="Quick Setup Guide"
        icon={
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        }
        variant="primary"
      >
        <div className="space-y-4">
          <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-[#66b077]/20">
            <h4 className="font-bold text-[#1b130e] mb-4 flex items-center">
              <span className="w-8 h-8 bg-[#66b077] text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">1</span>
              Add DNS Record
            </h4>
            <div className="space-y-2 text-sm text-[#4a3728] ml-11">
              <p>• Log into your DNS provider (Cloudflare, GoDaddy, etc.)</p>
              <p>• Navigate to DNS management for <strong>{result.domain}</strong></p>
              <p>• Add a new TXT record with the name and value shown above</p>
              <p>• Set TTL to 3600 seconds (or your provider's default)</p>
            </div>
          </div>

          <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-[#66b077]/20">
            <h4 className="font-bold text-[#1b130e] mb-4 flex items-center">
              <span className="w-8 h-8 bg-[#66b077] text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">2</span>
              Configure Email Server
            </h4>
            <div className="space-y-2 text-sm text-[#4a3728] ml-11">
              <p>• Install the private key in your email server's DKIM configuration</p>
              <p>• Set the selector to <strong>{result.selector}</strong></p>
              <p>• Enable DKIM signing for outgoing emails</p>
              <p>• Test the configuration with a test email</p>
            </div>
          </div>

          <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-[#66b077]/20">
            <h4 className="font-bold text-[#1b130e] mb-4 flex items-center">
              <span className="w-8 h-8 bg-[#66b077] text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">3</span>
              Verify Implementation
            </h4>
            <div className="space-y-2 text-sm text-[#4a3728] ml-11">
              <p>• Wait for DNS propagation (up to 48 hours)</p>
              <p>• Use the DNS validation tool below to verify the record</p>
              <p>• Test with our Email Tester Tool for complete verification</p>
            </div>
          </div>
        </div>
      </CollapsibleSection>

      {/* Detailed Setup Instructions - Collapsible */}
      <CollapsibleSection
        title="Detailed Setup Instructions"
        icon={
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        }
        variant="secondary"
      >
        <div className="space-y-6">
          <div className="prose prose-sm max-w-none">
            <h4 className="font-bold text-[#1b130e] mb-3">DNS Provider Specific Instructions:</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white/60 rounded-lg p-4 border border-[#4a3728]/10">
                <h5 className="font-semibold text-[#1b130e] mb-2">Cloudflare</h5>
                <ul className="text-xs text-[#4a3728] space-y-1">
                  <li>• Go to DNS → Records → Add record</li>
                  <li>• Type: TXT</li>
                  <li>• Name: {result.selector}._domainkey</li>
                  <li>• Content: [Copy from above]</li>
                  <li>• TTL: Auto</li>
                </ul>
              </div>
              <div className="bg-white/60 rounded-lg p-4 border border-[#4a3728]/10">
                <h5 className="font-semibold text-[#1b130e] mb-2">GoDaddy</h5>
                <ul className="text-xs text-[#4a3728] space-y-1">
                  <li>• Go to DNS Management</li>
                  <li>• Add new record → TXT</li>
                  <li>• Host: {result.selector}._domainkey</li>
                  <li>• TXT Value: [Copy from above]</li>
                  <li>• TTL: 1 Hour</li>
                </ul>
              </div>
              <div className="bg-white/60 rounded-lg p-4 border border-[#4a3728]/10">
                <h5 className="font-semibold text-[#1b130e] mb-2">Namecheap</h5>
                <ul className="text-xs text-[#4a3728] space-y-1">
                  <li>• Go to Advanced DNS</li>
                  <li>• Add new record → TXT Record</li>
                  <li>• Host: {result.selector}._domainkey</li>
                  <li>• Value: [Copy from above]</li>
                  <li>• TTL: Automatic</li>
                </ul>
              </div>
              <div className="bg-white/60 rounded-lg p-4 border border-[#4a3728]/10">
                <h5 className="font-semibold text-[#1b130e] mb-2">Route 53</h5>
                <ul className="text-xs text-[#4a3728] space-y-1">
                  <li>• Go to Hosted zones</li>
                  <li>• Create record → TXT</li>
                  <li>• Record name: {result.selector}._domainkey</li>
                  <li>• Value: [Copy from above]</li>
                  <li>• TTL: 300</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="bg-[#f3ece8] rounded-xl p-6 border border-[#4a3728]/20">
            <h4 className="font-bold text-[#1b130e] mb-3">Email Server Configuration:</h4>
            <div className="space-y-3 text-sm text-[#4a3728]">
              <p><strong>For Postfix:</strong> Add the private key to your DKIM configuration and update main.cf with the selector and domain settings.</p>
              <p><strong>For Exim:</strong> Configure the DKIM settings in your Exim configuration file with the private key path and selector.</p>
              <p><strong>For Microsoft Exchange:</strong> Use the Exchange Admin Center to configure DKIM with the generated keys.</p>
              <p><strong>For cPanel/WHM:</strong> Use the Email Authentication section to add the DKIM keys and enable signing.</p>
            </div>
          </div>

          <div className="bg-[#66b077]/5 rounded-xl p-6 border border-[#66b077]/20">
            <h4 className="font-bold text-[#1b130e] mb-3">Troubleshooting:</h4>
            <div className="space-y-2 text-sm text-[#4a3728]">
              <p>• <strong>DNS not propagating:</strong> Wait up to 48 hours for global DNS propagation</p>
              <p>• <strong>Record not found:</strong> Verify the record name matches exactly: {result.recordName}</p>
              <p>• <strong>Invalid signature:</strong> Ensure the private key is correctly installed on your email server</p>
              <p>• <strong>Multiple records:</strong> Remove any duplicate DKIM records for the same selector</p>
            </div>
          </div>
        </div>
      </CollapsibleSection>

      {/* DNS Validation - Enhanced */}
      <div className="bg-white/80 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-4 shadow-sm">
        <div className="flex items-center space-x-3 mb-3">
          <div className="w-8 h-8 bg-[#66b077]/10 rounded-lg flex items-center justify-center">
            <svg className="w-4 h-4 text-[#66b077]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-bold text-[#1b130e]">DNS Validation</h3>
        </div>
        <p className="text-[#4a3728] mb-4 text-sm">
          After adding the DNS record, use this tool to verify it's properly configured and accessible globally.
        </p>
        <button
          onClick={() => onValidate?.(result.domain, result.selector, result.publicKey)}
          disabled={validating}
          className="bg-gradient-to-r from-[#66b077] to-[#07880e] text-white px-8 py-4 rounded-xl hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center font-semibold"
        >
          {validating ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
              Validating DNS Record...
            </>
          ) : (
            <>
              <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Validate DNS Record
            </>
          )}
        </button>
      </div>

      {/* Next Steps - Enhanced */}
      <div className="bg-gradient-to-r from-[#66b077]/5 to-[#07880e]/5 border border-[#66b077]/20 rounded-2xl p-4">
        <div className="flex items-center space-x-3 mb-3">
          <div className="w-8 h-8 bg-[#66b077]/10 rounded-lg flex items-center justify-center">
            <svg className="w-4 h-4 text-[#66b077]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
            </svg>
          </div>
          <h3 className="text-lg font-bold text-[#1b130e]">Next Steps</h3>
        </div>
        <p className="text-[#4a3728] mb-4 text-sm">
          Complete your email authentication setup and verify your DKIM implementation with our integrated tools.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <a
            href="/tools/email-tester"
            className="group bg-white/60 backdrop-blur-sm border border-[#66b077]/20 rounded-xl p-6 hover:bg-[#66b077]/5 transition-all duration-200 hover:shadow-md"
          >
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-[#66b077]/10 rounded-xl flex items-center justify-center group-hover:bg-[#66b077]/20 transition-colors">
                <svg className="w-6 h-6 text-[#66b077]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-[#1b130e] mb-1">Email Tester Tool</h4>
                <p className="text-sm text-[#4a3728]">Test your DKIM implementation with real email analysis</p>
              </div>
              <svg className="w-5 h-5 text-[#4a3728] group-hover:text-[#66b077] transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </a>
          <a
            href="/tools/dmarc-generator"
            className="group bg-white/60 backdrop-blur-sm border border-[#4a3728]/20 rounded-xl p-6 hover:bg-[#4a3728]/5 transition-all duration-200 hover:shadow-md"
          >
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-[#4a3728]/10 rounded-xl flex items-center justify-center group-hover:bg-[#4a3728]/20 transition-colors">
                <svg className="w-6 h-6 text-[#4a3728]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-[#1b130e] mb-1">DMARC Generator</h4>
                <p className="text-sm text-[#4a3728]">Create DMARC policies to complete your email security</p>
              </div>
              <svg className="w-5 h-5 text-[#4a3728] group-hover:text-[#4a3728] transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </a>
        </div>
      </div>
    </div>
  );
}
