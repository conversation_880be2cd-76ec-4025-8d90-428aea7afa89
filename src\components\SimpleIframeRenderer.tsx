'use client';

import React, { useEffect, useState, useRef } from 'react';

interface SimpleIframeRendererProps {
  /**
   * The HTML content to render in the iframe
   */
  html: string;

  /**
   * Optional className for the iframe container
   */
  className?: string;

  /**
   * Optional style object for the iframe
   */
  style?: React.CSSProperties;

  /**
   * Whether this is a guide email (to skip showing skeleton)
   */
  isGuideEmail?: boolean;
}

/**
 * SimpleIframeRenderer Component
 *
 * A simple component for rendering HTML content in an iframe without any external dependencies.
 * Features:
 * - Complete style isolation through iframe
 * - Automatic height adjustment
 * - Server-side rendering compatibility
 */
export default function SimpleIframeRenderer({
  html,
  className = '',
  style = {},
  isGuideEmail = false
}: SimpleIframeRendererProps) {
  // Create a ref for the iframe
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // State to track if the component is mounted (for SSR compatibility)
  const [isMounted, setIsMounted] = useState(false);

  // State to track loading status
  const [isLoading, setIsLoading] = useState(true);

  // Effect to handle client-side rendering
  useEffect(() => {
    // Use requestAnimationFrame to ensure we're in the browser environment
    // and to avoid hydration mismatches
    const timeoutId = setTimeout(() => {
      setIsMounted(true);
    }, 0);

    return () => clearTimeout(timeoutId);
  }, []);

  // Effect to handle iframe initialization and content
  useEffect(() => {
    if (!isMounted) return;

    const iframe = iframeRef.current;
    if (!iframe) return;

    try {
      // We're using srcdoc approach which doesn't require direct access to the iframe document

      // Process HTML to handle iframes with CSP restrictions
      let processedHtml = html;

      // Simple regex to find iframes with Google domains
      const googleIframeRegex = /<iframe[^>]*src=["'](https?:\/\/(www\.)?google\.com\/[^"']*)["'][^>]*><\/iframe>/gi;

      // Replace Google iframes with a placeholder and link
      processedHtml = processedHtml.replace(googleIframeRegex, (_match, url) => {
        return `<div style="border: 1px solid #ddd; padding: 16px; margin: 8px 0; text-align: center; background-color: #f9f9f9;">
          <p style="margin-bottom: 8px;">This content from Google cannot be displayed due to security restrictions.</p>
          <a href="${url}" target="_blank" style="color: #1a73e8; text-decoration: underline;">Open content in a new tab</a>
        </div>`;
      });

      // Create a complete HTML document with necessary styles
      const htmlContent = `
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests; default-src 'self' 'unsafe-inline' https: data:; img-src * data: blob: https:; font-src * data: https:; style-src 'self' 'unsafe-inline' https:;">
            <title>Email Content</title>
            <style>
              /* Reset styles */
              html, body {
                margin: 0;
                padding: 0;
                overflow-y: visible; /* Allow content to expand without vertical scrollbars */
                overflow-x: auto; /* Allow horizontal scrolling when needed */
                height: auto; /* Allow height to adjust to content */
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
                line-height: 1.5;
                color: #333;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
              }

              /* Email wrapper */
              .email-wrapper {
                width: 100%;
                max-width: 100%;
                margin: 0 auto;
                padding: 0;
                /* Ensure content can be scrolled horizontally if needed */
                display: inline-block;
                min-width: min-content;
              }

              /* Make sure images are responsive */
              img {
                max-width: 100%;
                height: auto;
              }

              /* Ensure tables are responsive */
              table {
                max-width: 100%;
              }
            </style>
            <script>
              // Function to adjust iframe height and handle horizontal content
              function adjustHeight() {
                // Get the height of the content
                const height = document.body.scrollHeight;

                // Get the width of the content to check if horizontal scrolling is needed
                const contentWidth = document.body.scrollWidth;
                const viewportWidth = window.innerWidth;

                // Send height and width information to parent
                window.parent.postMessage({
                  type: 'resize-iframe',
                  height: height,
                  contentWidth: contentWidth,
                  viewportWidth: viewportWidth,
                  needsHorizontalScroll: contentWidth > viewportWidth
                }, '*');
              }

              // Listen for load event
              window.addEventListener('load', function() {
                // Adjust height after images and other resources are loaded
                adjustHeight();

                // Add event listeners to all images
                document.querySelectorAll('img').forEach(function(img) {
                  img.addEventListener('load', adjustHeight);
                });

                // Handle links to make them work properly
                document.querySelectorAll('a[href]').forEach(function(link) {
                  link.addEventListener('click', function(e) {
                    e.preventDefault();

                    var href = this.getAttribute('href');

                    // Open links in new tab/window
                    if (href && !href.startsWith('#') && !href.startsWith('javascript:')) {
                      window.open(href, '_blank');
                    }
                  });
                });
              });
            </script>
          </head>
          <body>
            <div class="email-wrapper">
              ${processedHtml}
            </div>
          </body>
        </html>
      `;

      // Since we're removing allow-same-origin, we need to use srcdoc instead of document.write
      // This provides the same functionality but works with the more restrictive sandbox
      iframe.srcdoc = htmlContent;

      // Function to handle iframe resize messages
      const handleMessage = (event: MessageEvent) => {
        if (event.data && event.data.type === 'resize-iframe' && iframe) {
          // Set the iframe height
          iframe.style.height = `${event.data.height}px`;

          // Handle horizontal scrolling information
          if (event.data.needsHorizontalScroll) {
            // Ensure the container allows horizontal scrolling
            const container = iframe.parentElement;
            if (container) {
              container.style.overflowX = 'auto';
            }

            // Make sure the iframe allows horizontal scrolling
            iframe.style.overflowX = 'auto';
            iframe.style.maxWidth = '100%';

            console.log('Horizontal scrolling enabled for content width:', event.data.contentWidth);
          }

          setIsLoading(false);
        }
      };

      // Add message event listener
      window.addEventListener('message', handleMessage);

      // Set a timeout to ensure loading state is cleared even if no resize message is received
      const timeoutId = setTimeout(() => {
        setIsLoading(false);

        // Set a default height if no message was received
        if (iframe.style.height === '') {
          iframe.style.height = '500px';
        }
      }, 1000);

      // Cleanup
      return () => {
        window.removeEventListener('message', handleMessage);
        clearTimeout(timeoutId);
      };
    } catch (error) {
      console.error('Error rendering email in iframe:', error);
      setIsLoading(false);
    }
  }, [isMounted, html]);

  // Loading state component
  const loadingState = (
    <div className="animate-pulse flex flex-col space-y-4 p-4" style={{ minHeight: '200px' }}>
      <div className="h-4 bg-neutral-200 rounded w-3/4"></div>
      <div className="h-4 bg-neutral-200 rounded w-full"></div>
      <div className="h-4 bg-neutral-200 rounded w-5/6"></div>
      <div className="h-4 bg-neutral-200 rounded w-4/6"></div>
      <div className="h-4 bg-neutral-200 rounded w-3/4"></div>
    </div>
  );

  return (
    <div
      className={`simple-iframe-container ${className}`}
      style={{
        width: '100%',
        overflowY: 'visible', // Allow content to expand vertically
        overflowX: 'auto', // Allow horizontal scrolling when needed
        position: 'relative',
        transition: 'opacity 0.3s ease-in-out',
        WebkitOverflowScrolling: 'touch', // Smooth scrolling on iOS
        msOverflowStyle: '-ms-autohiding-scrollbar' // Better scrollbars on Edge
      }}
    >
      {!isGuideEmail && (!isMounted || isLoading) ? (
        // Server-side and initial client render - show loading state (skip for guide emails)
        loadingState
      ) : null}

      {/* Always render the iframe, but hide it until loaded */}
      <iframe
        ref={iframeRef}
        title="Email content"
        style={{
          width: '100%',
          border: 'none',
          overflowY: 'hidden', // Hide vertical scrollbar
          overflowX: 'auto', // Allow horizontal scrolling
          minHeight: '200px', // Lower minimum height to allow for shorter emails
          display: 'block',
          transition: 'opacity 0.3s ease-in-out, height 0.3s ease-in-out',
          transform: 'translateZ(0)', // Force hardware acceleration
          opacity: isMounted && !isLoading ? 1 : 0,
          WebkitOverflowScrolling: 'touch', // Smooth scrolling on iOS
          msOverflowStyle: '-ms-autohiding-scrollbar', // Better scrollbars on Edge
          ...style
        }}
        scrolling="no" // HTML attribute to disable vertical scrolling (for older browsers)
        sandbox="allow-scripts allow-popups"
      />
    </div>
  );
}
