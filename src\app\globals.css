@import "tailwindcss";
@import "./animations.css";

/* Inter font is loaded via Next.js font optimization in layout.tsx */

:root {
  /* Earth-tone UI color palette */
  --background: #fbfaf8;
  --surface: #fbfaf8;
  --foreground: #1b130e;
  --foreground-light: #4a3728;
  --foreground-lighter: #956b50;

  /* Primary colors - Earth-tone browns */
  --primary-50: #fbfaf8;
  --primary-100: #f3ece8;
  --primary-200: #e6d5cc;
  --primary-300: #d4bfb0;
  --primary-400: #c1a894;
  --primary-500: #956b50;
  --primary-600: #4a3728;
  --primary-700: #3a2b1f;
  --primary-800: #2a1f16;
  --primary-900: #1b130e;

  /* Neutral colors - Earth-tone browns */
  --neutral-50: #fbfaf8;
  --neutral-100: #f3ece8;
  --neutral-200: #e6d5cc;
  --neutral-300: #d4bfb0;
  --neutral-400: #c1a894;
  --neutral-500: #956b50;
  --neutral-600: #4a3728;
  --neutral-700: #3a2b1f;
  --neutral-800: #1b130e;
  --neutral-900: #0f0a07;

  /* Status colors - Earth-tone palette */
  --success-500: #66b077;
  --error-500: #f59e0b;
  --warning-500: #f59e0b;
  --info-500: #956b50;

  /* Earth-tone specific colors */
  --earth-beige-light: #fbfaf8;
  --earth-beige-secondary: #f3ece8;
  --earth-brown-dark: #1b130e;
  --earth-brown-medium: #4a3728;
  --earth-brown-muted: #956b50;
  --earth-green-success: #66b077;
  --earth-amber-accent: #f59e0b;

  /* Brand colors - Maintain orange for recognition */
  --brand-orange: #ce601c;
  --brand-orange-light: #e67e22;
  --brand-orange-hover: #faefe8;

  /* SVG background */
  --svg-background: #f3ece8;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -2px rgba(0, 0, 0, 0.03);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04), 0 4px 6px -4px rgba(0, 0, 0, 0.02);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.03), 0 8px 10px -6px rgba(0, 0, 0, 0.01);

  /* Border radius */
  --radius-sm: 0.25rem;
  --radius: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;

  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;

  /* Typography */
  --font-sans: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-mono: 'SF Mono', ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;

  /* For backward compatibility */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary-600);
  --color-primary-light: var(--primary-500);
  --color-primary-dark: var(--primary-700);
  --gray-50: var(--neutral-50);
  --gray-100: var(--neutral-100);
  --gray-200: var(--neutral-200);
  --gray-300: var(--neutral-300);
  --gray-400: var(--neutral-400);
  --gray-500: var(--neutral-500);
  --gray-600: var(--neutral-600);
  --gray-700: var(--neutral-700);
  --gray-800: var(--neutral-800);
  --gray-900: var(--neutral-900);
}

/* Dark mode disabled */

/* Base styles */
body {
  background: var(--background);
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23f3ece8' fill-opacity='0.3' fill-rule='evenodd'/%3E%3C/svg%3E");
  color: var(--foreground);
  font-family: var(--font-sans);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  letter-spacing: -0.011em;
}

/* Modern button styles */
button,
.btn,
[type="button"],
[type="submit"] {
  border-radius: var(--radius);
  font-weight: 500;
  transition: all 0.2s ease;
}

button:active,
.btn:active,
[type="button"]:active,
[type="submit"]:active {
  transform: scale(0.98);
}

/* Input styles */
input,
select,
textarea {
  border-radius: var(--radius);
  border-color: var(--neutral-200);
  transition: all 0.2s ease;
}

input:focus,
select:focus,
textarea:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 2px var(--primary-50);
}

/* Card styles */
.card {
  background: var(--surface);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow);
  transition: box-shadow 0.2s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--neutral-100);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--neutral-300);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-400);
}

/* Chart styles - Remove hover effect */
.recharts-wrapper .recharts-layer.recharts-bar-rectangle:hover {
  filter: none !important;
  opacity: 1 !important;
}

.recharts-wrapper .recharts-layer.recharts-bar-rectangle .recharts-rectangle:hover {
  filter: none !important;
  opacity: 1 !important;
}

.recharts-wrapper .recharts-rectangle.recharts-bar-rectangle:hover {
  filter: none !important;
  opacity: 1 !important;
}

/* Completely disable hover effects */
.recharts-wrapper .recharts-surface .recharts-layer:hover {
  filter: none !important;
  opacity: 1 !important;
}

/* Target the specific gray background */
.recharts-tooltip-cursor {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

/* Email content styles for iframe renderers */
.email-iframe {
  /* Ensure proper iframe styling */
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

/* Fix for MutationObserver issues */
.email-content iframe {
  display: block !important;
  border: none !important;
  width: 100% !important;
  position: relative !important;
  z-index: 1 !important;
}

/* Email content wrapper responsive styles */
.email-content-wrapper {
  --mobile-width: 100%;
  --tablet-width: 540px;
  --desktop-width: 600px;
}

/* Responsive container for email content */
.email-content-container {
  max-width: var(--mobile-width);
  margin: 0 auto;
  overflow-x: auto;
  overflow-y: visible;
  width: 100%;
}

/* Media queries for responsive email content */
@media (min-width: 640px) {
  .email-content-container {
    max-width: var(--tablet-width);
  }
}

@media (min-width: 1024px) {
  .email-content-container {
    max-width: var(--desktop-width);
  }
}

/* Fix for mobile overflow issues */
@media (max-width: 639px) {
  .email-content img {
    max-width: 100% !important;
    height: auto !important;
  }
}

/* Animation for email list item click */
.scale-98 {
  transform: scale(0.98);
  transition: transform 0.1s ease-in-out;
}

/* Animation for button click */
.active\:scale-98:active {
  transform: scale(0.98);
  transition: transform 0.1s ease-in-out;
}

/* Earth-tone button hover effects */
.contact-button:hover {
  background-color: var(--earth-beige-secondary) !important;
}

/* Social media icon hover effects */
.social-icon:hover {
  background-color: var(--brand-orange-hover) !important;
  color: var(--brand-orange) !important;
}

/* Animation for fade in */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.2s ease-out;
}
