'use client';

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

// Define badge variants using class-variance-authority
const badgeVariants = cva(
  // Base styles applied to all badges
  "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-colors",
  {
    variants: {
      variant: {
        default: "bg-neutral-100 text-neutral-800",
        primary: "bg-primary-100 text-primary-800",
        secondary: "bg-neutral-100 text-neutral-800",
        success: "bg-success-100 text-success-600",
        warning: "bg-warning-100 text-warning-600",
        danger: "bg-error-100 text-error-600",
        info: "bg-info-100 text-info-600",
      },
      outline: {
        true: "bg-transparent border",
      },
    },
    compoundVariants: [
      {
        variant: "default",
        outline: true,
        className: "border-neutral-200 text-neutral-800",
      },
      {
        variant: "primary",
        outline: true,
        className: "border-primary-200 text-primary-800",
      },
      {
        variant: "secondary",
        outline: true,
        className: "border-neutral-200 text-neutral-800",
      },
      {
        variant: "success",
        outline: true,
        className: "border-success-100 text-success-600",
      },
      {
        variant: "warning",
        outline: true,
        className: "border-warning-100 text-warning-600",
      },
      {
        variant: "danger",
        outline: true,
        className: "border-error-100 text-error-600",
      },
      {
        variant: "info",
        outline: true,
        className: "border-info-100 text-info-600",
      },
    ],
    defaultVariants: {
      variant: "default",
      outline: false,
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLSpanElement>,
    VariantProps<typeof badgeVariants> {}

const Badge = React.forwardRef<HTMLSpanElement, BadgeProps>(
  ({ className, variant, outline, ...props }, ref) => {
    return (
      <span
        className={cn(badgeVariants({ variant, outline, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);

Badge.displayName = "Badge";

export { Badge, badgeVariants };
