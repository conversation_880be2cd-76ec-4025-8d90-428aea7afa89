/**
 * SMTP Results Component
 *
 * Displays SMTP test results with authentication analysis
 */

'use client';

import React, { useState } from 'react';
import { SmtpTestResult } from '@/types/smtp';

interface SmtpResultsProps {
  result: SmtpTestResult;
  testMode: 'auto' | 'custom';
}

export default function SmtpResults({ result, testMode }: SmtpResultsProps) {
  const [showLogs, setShowLogs] = useState(false);
  const [copiedLogs, setCopiedLogs] = useState(false);

  const copyLogsToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(result.logs);
      setCopiedLogs(true);
      setTimeout(() => setCopiedLogs(false), 2000);
    } catch (error) {
      console.error('Failed to copy logs:', error);
    }
  };

  const getStatusIcon = (success: boolean) => {
    if (success) {
      return (
        <svg className="w-6 h-6 text-[#66b077]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      );
    } else {
      return (
        <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      );
    }
  };

  // Helper function specifically for PTR (Reverse DNS) status determination
  const getPtrStatus = (ptrData: any): 'pass' | 'warning' | 'fail' => {
    if (!ptrData) return 'fail';

    // Check direct result field
    const result = ptrData.result || ptrData.status;

    // Direct result check - Email Tester Tool uses these exact values
    if (result === 'pass') return 'pass';
    if (result === 'warning') return 'warning';
    if (result === 'fail') return 'fail';

    // Check for analysis structure
    if (ptrData.analysis?.reverseDns?.status === 'pass') return 'pass';
    if (ptrData.analysis?.reverseDns?.status === 'warning') return 'warning';
    if (ptrData.analysis?.reverseDns?.status === 'fail') return 'fail';

    // Special handling: If PTR has a hostname but has issues, it's likely a warning state
    // This matches the Email Tester Tool logic where PTR passes with warnings
    if (ptrData.hostname && ptrData.issues?.length > 0) {
      return 'warning';
    }

    // If PTR has a hostname but no explicit result, assume it's working (pass)
    if (ptrData.hostname) {
      return 'pass';
    }

    // Default to fail if we can't determine status
    return 'fail';
  };

  // Helper function to get status text
  const getStatusText = (status: 'pass' | 'warning' | 'fail'): string => {
    switch (status) {
      case 'pass': return 'Passed';
      case 'warning': return 'Warning';
      case 'fail': return 'Failed';
      default: return 'Failed';
    }
  };

  const getAuthStatusIcon = (status: 'pass' | 'warning' | 'fail') => {
    if (status === 'pass') {
      return (
        <svg className="w-5 h-5 text-[#66b077]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      );
    } else if (status === 'warning') {
      return (
        <svg className="w-5 h-5 text-[#f59e0b]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      );
    } else {
      return (
        <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      );
    }
  };

  return (
    <div className="space-y-4">
      {/* Main Result Status */}
      <div className="bg-white/80 backdrop-blur-sm rounded-lg border border-[#4a3728]/20 p-4">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-[#f59e0b] to-[#ce601c] flex items-center justify-center">
            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2-2z" />
            </svg>
          </div>
          <div>
            <h3 className="text-xl font-bold" style={{ color: '#1b130e' }}>Test Results</h3>
            <p className="text-sm" style={{ color: '#4a3728' }}>SMTP connection test results</p>
          </div>
        </div>

        <div className="flex items-center gap-3 p-4 rounded-lg" style={{ backgroundColor: result.success ? '#f0f9ff' : '#fef2f2' }}>
          {getStatusIcon(result.success)}
          <div className="flex-1">
            <h4 className="font-semibold" style={{ color: result.success ? '#166534' : '#dc2626' }}>
              {result.success ? 'SMTP Test Successful' : 'SMTP Test Failed'}
            </h4>
            <p className="text-sm" style={{ color: result.success ? '#15803d' : '#dc2626' }}>
              {result.success
                ? `Email sent successfully${result.messageId ? ` (ID: ${result.messageId})` : ''}`
                : result.error || 'SMTP connection failed'
              }
            </p>
            {result.testAddress && (
              <p className="text-sm mt-1" style={{ color: '#4a3728' }}>
                Test email sent to: <span className="font-mono">{result.testAddress}</span>
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Email Authentication Analysis (Auto Mode Only) */}
      {testMode === 'auto' && (
        <div className="bg-white/80 backdrop-blur-sm rounded-lg border border-[#4a3728]/20 p-4">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-[#66b077] to-[#07880e] flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <div>
              <h3 className="text-xl font-bold" style={{ color: '#1b130e' }}>Email Authentication Analysis</h3>
              <p className="text-sm" style={{ color: '#4a3728' }}>SPF, DKIM, DMARC, MX Records, and Reverse DNS validation results</p>
            </div>
          </div>

          {result.analysisResults ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 mb-4">
            {/* SPF */}
            <div className="p-3 rounded-lg border border-[#4a3728]/20" style={{ backgroundColor: '#fbfaf8' }}>
              <div className="flex items-center gap-2 mb-2">
                {(() => {
                  const spfPassed = result.analysisResults.spf?.pass ||
                    result.analysisResults.spf?.result === 'pass' ||
                    result.analysisResults.analysis?.spf?.status === 'pass' ||
                    false;
                  return getAuthStatusIcon(spfPassed ? 'pass' : 'fail');
                })()}
                <span className="font-semibold" style={{ color: '#1b130e' }}>SPF</span>
              </div>
              <p className="text-sm" style={{ color: '#4a3728' }}>
                {(result.analysisResults.spf?.pass ||
                  result.analysisResults.spf?.result === 'pass' ||
                  result.analysisResults.analysis?.spf?.status === 'pass') ? 'Passed' : 'Failed'}
                {(result.analysisResults.spf?.reason ||
                  result.analysisResults.analysis?.spf?.details) &&
                  `: ${result.analysisResults.spf?.reason || result.analysisResults.analysis?.spf?.details}`}
              </p>
            </div>

            {/* DKIM */}
            <div className="p-3 rounded-lg border border-[#4a3728]/20" style={{ backgroundColor: '#fbfaf8' }}>
              <div className="flex items-center gap-2 mb-2">
                {(() => {
                  const dkimPassed = result.analysisResults.dkim?.pass ||
                    result.analysisResults.dkim?.result === 'pass' ||
                    result.analysisResults.analysis?.dkim?.status === 'pass' ||
                    false;
                  return getAuthStatusIcon(dkimPassed ? 'pass' : 'fail');
                })()}
                <span className="font-semibold" style={{ color: '#1b130e' }}>DKIM</span>
              </div>
              <p className="text-sm" style={{ color: '#4a3728' }}>
                {(result.analysisResults.dkim?.pass ||
                  result.analysisResults.dkim?.result === 'pass' ||
                  result.analysisResults.analysis?.dkim?.status === 'pass') ? 'Passed' : 'Failed'}
                {(result.analysisResults.dkim?.reason ||
                  result.analysisResults.analysis?.dkim?.details) &&
                  `: ${result.analysisResults.dkim?.reason || result.analysisResults.analysis?.dkim?.details}`}
              </p>
            </div>

            {/* DMARC */}
            <div className="p-3 rounded-lg border border-[#4a3728]/20" style={{ backgroundColor: '#fbfaf8' }}>
              <div className="flex items-center gap-2 mb-2">
                {(() => {
                  const dmarcPassed = result.analysisResults.dmarc?.pass ||
                    result.analysisResults.dmarc?.result === 'pass' ||
                    result.analysisResults.analysis?.dmarc?.status === 'pass' ||
                    false;
                  return getAuthStatusIcon(dmarcPassed ? 'pass' : 'fail');
                })()}
                <span className="font-semibold" style={{ color: '#1b130e' }}>DMARC</span>
              </div>
              <p className="text-sm" style={{ color: '#4a3728' }}>
                {(result.analysisResults.dmarc?.pass ||
                  result.analysisResults.dmarc?.result === 'pass' ||
                  result.analysisResults.analysis?.dmarc?.status === 'pass') ? 'Passed' : 'Failed'}
                {(result.analysisResults.dmarc?.reason ||
                  result.analysisResults.analysis?.dmarc?.details) &&
                  `: ${result.analysisResults.dmarc?.reason || result.analysisResults.analysis?.dmarc?.details}`}
              </p>
            </div>

            {/* MX Records */}
            <div className="p-3 rounded-lg border border-[#4a3728]/20" style={{ backgroundColor: '#fbfaf8' }}>
              <div className="flex items-center gap-2 mb-2">
                {(() => {
                  const mxPassed = result.analysisResults.mx?.result === 'pass' ||
                    result.analysisResults.enhancedAuthResults?.mx?.result === 'pass' ||
                    result.analysisResults.analysis?.mx?.status === 'pass' ||
                    false;
                  return getAuthStatusIcon(mxPassed ? 'pass' : 'fail');
                })()}
                <span className="font-semibold" style={{ color: '#1b130e' }}>MX Records</span>
              </div>
              <p className="text-sm" style={{ color: '#4a3728' }}>
                {(result.analysisResults.mx?.result === 'pass' ||
                  result.analysisResults.enhancedAuthResults?.mx?.result === 'pass' ||
                  result.analysisResults.analysis?.mx?.status === 'pass') ? 'Passed' : 'Failed'}
                {(result.analysisResults.mx?.info ||
                  result.analysisResults.enhancedAuthResults?.mx?.info ||
                  result.analysisResults.analysis?.mx?.details) &&
                  `: ${result.analysisResults.mx?.info ||
                       result.analysisResults.enhancedAuthResults?.mx?.info ||
                       result.analysisResults.analysis?.mx?.details}`}
              </p>
            </div>

            {/* Reverse DNS */}
            <div className="p-3 rounded-lg border border-[#4a3728]/20" style={{ backgroundColor: '#fbfaf8' }}>
              <div className="flex items-center gap-2 mb-2">
                {(() => {
                  const ptrStatus = getPtrStatus(
                    result.analysisResults.reverseDns ||
                    result.analysisResults.enhancedAuthResults?.reverseDns ||
                    result.analysisResults.analysis?.reverseDns
                  );
                  return getAuthStatusIcon(ptrStatus);
                })()}
                <span className="font-semibold" style={{ color: '#1b130e' }}>Reverse DNS</span>
              </div>
              <p className="text-sm" style={{ color: '#4a3728' }}>
                {(() => {
                  const ptrStatus = getPtrStatus(
                    result.analysisResults.reverseDns ||
                    result.analysisResults.enhancedAuthResults?.reverseDns ||
                    result.analysisResults.analysis?.reverseDns
                  );
                  return getStatusText(ptrStatus);
                })()}
                {(result.analysisResults.reverseDns?.info ||
                  result.analysisResults.reverseDns?.hostname ||
                  result.analysisResults.enhancedAuthResults?.reverseDns?.info ||
                  result.analysisResults.enhancedAuthResults?.reverseDns?.hostname ||
                  result.analysisResults.analysis?.reverseDns?.details) &&
                  `: ${result.analysisResults.reverseDns?.info ||
                       result.analysisResults.reverseDns?.hostname ||
                       result.analysisResults.enhancedAuthResults?.reverseDns?.info ||
                       result.analysisResults.enhancedAuthResults?.reverseDns?.hostname ||
                       result.analysisResults.analysis?.reverseDns?.details}`}
              </p>
            </div>
          </div>

          {/* Overall Score */}
          {(result.analysisResults.score !== undefined || result.analysisResults.overallScore !== undefined) && (
            <div className="p-3 rounded-lg border border-[#4a3728]/20" style={{ backgroundColor: '#f3ece8' }}>
              <div className="flex items-center justify-between">
                <span className="font-semibold" style={{ color: '#1b130e' }}>Overall Score</span>
                <span className="text-2xl font-bold" style={{
                  color: ((result.analysisResults.score ?? result.analysisResults.overallScore ?? 0) >= 8) ? '#66b077' :
                         ((result.analysisResults.score ?? result.analysisResults.overallScore ?? 0) >= 6) ? '#f59e0b' : '#dc2626'
                }}>
                  {result.analysisResults.score ?? result.analysisResults.overallScore}/10
                </span>
              </div>
            </div>
          )}

          {/* Recommendations */}
          {result.analysisResults.recommendations && result.analysisResults.recommendations.length > 0 && (
            <div className="mt-4">
              <h4 className="font-semibold mb-2" style={{ color: '#1b130e' }}>Recommendations</h4>
              <ul className="space-y-2">
                {result.analysisResults.recommendations.map((recommendation, index) => {
                  // Handle both string recommendations and object recommendations
                  const isObject = typeof recommendation === 'object' && recommendation !== null;
                  const recommendationText = isObject
                    ? (recommendation as any).recommendation || (recommendation as any).issue || 'Recommendation available'
                    : recommendation;

                  return (
                    <li key={index} className="text-sm" style={{ color: '#4a3728' }}>
                      <div className="flex items-start gap-2">
                        <svg className="w-4 h-4 mt-0.5 flex-shrink-0" style={{ color: '#f59e0b' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <div className="flex-1">
                          <p className="font-medium">{recommendationText}</p>
                          {isObject && (recommendation as any).category && (
                            <p className="text-xs mt-1" style={{ color: '#956b50' }}>
                              Category: {(recommendation as any).category}
                            </p>
                          )}
                          {isObject && (recommendation as any).priority && (
                            <p className="text-xs mt-1" style={{ color: '#956b50' }}>
                              Priority: {(recommendation as any).priority}
                            </p>
                          )}
                        </div>
                      </div>
                    </li>
                  );
                })}
              </ul>
            </div>
          )}
            </>
          ) : (
            <div className="p-4 rounded-lg border border-[#f59e0b]/30" style={{ backgroundColor: '#fffbeb' }}>
              <div className="flex items-center gap-2 mb-2">
                <svg className="w-5 h-5 text-[#f59e0b]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <span className="font-semibold text-[#1b130e]">Analysis Pending</span>
              </div>
              <p className="text-sm text-[#4a3728]">
                Email authentication analysis is still processing. Please check back in a few moments or try testing again.
              </p>
            </div>
          )}
        </div>
      )}

      {/* Diagnostic Logs */}
      <div className="bg-white/80 backdrop-blur-sm rounded-lg border border-[#4a3728]/20 p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-[#956b50] to-[#4a3728] flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-bold" style={{ color: '#1b130e' }}>Diagnostic Logs</h3>
              <p className="text-sm" style={{ color: '#4a3728' }}>Detailed SMTP connection logs</p>
            </div>
          </div>
          <div className="flex gap-2">
            <button
              onClick={copyLogsToClipboard}
              className="px-3 py-1 text-sm border border-[#4a3728] text-[#4a3728] rounded hover:bg-[#4a3728] hover:text-white transition-colors duration-200"
            >
              {copiedLogs ? 'Copied!' : 'Copy'}
            </button>
            <button
              onClick={() => setShowLogs(!showLogs)}
              className="px-3 py-1 text-sm border border-[#4a3728] text-[#4a3728] rounded hover:bg-[#4a3728] hover:text-white transition-colors duration-200"
            >
              {showLogs ? 'Hide' : 'Show'}
            </button>
          </div>
        </div>

        {showLogs && (
          <div className="bg-[#1b130e] text-[#fbfaf8] p-4 rounded-lg font-mono text-sm overflow-x-auto">
            <pre className="whitespace-pre-wrap">{result.logs}</pre>
          </div>
        )}
      </div>
    </div>
  );
}
