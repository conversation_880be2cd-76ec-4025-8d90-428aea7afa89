'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ArrowLeftIcon } from '@heroicons/react/24/solid';
import { Button } from '@/components/ui/Button';
import { PageHeader } from '@/components/ui/PageHeader';
import { Card, CardHeader, CardContent, CardTitle, CardDescription } from '@/components/ui/Card';
import { Select } from '@/components/ui/Select';

export default function AdLayoutDemoPage() {
  const [deviceType, setDeviceType] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');

  // Function to get container width based on device type
  const getContainerWidth = () => {
    switch (deviceType) {
      case 'mobile':
        return 'max-w-sm';
      case 'tablet':
        return 'max-w-2xl';
      case 'desktop':
      default:
        return 'max-w-5xl';
    }
  };

  // Function to render an ad placeholder
  const AdPlaceholder = ({
    placementId,
    height = '90px',
    width = '100%',
    description,
    deviceTypes = ['desktop', 'tablet', 'mobile']
  }: {
    placementId: string;
    height?: string;
    width?: string;
    description: string;
    deviceTypes?: string[];
  }) => {
    // Check if this ad should be shown on current device type
    if (!deviceTypes.includes(deviceType)) {
      return null;
    }

    return (
      <div
        className="border-2 border-dashed border-indigo-300 bg-indigo-50 rounded-md flex flex-col items-center justify-center p-2"
        style={{ height, width }}
      >
        <div className="text-sm font-medium text-indigo-700">Ad Placement: {placementId}</div>
        <div className="text-xs text-indigo-500 text-center">{description}</div>
      </div>
    );
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <PageHeader
        title="Ad Layout Demo"
        description="Visualize where different ad placements would appear in the Fademail interface"
        icon={
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
          </svg>
        }
        actions={
          <Link href="/management-portal-x7z9y2/ads">
            <Button variant="secondary" className="flex items-center">
              <ArrowLeftIcon className="h-4 w-4 mr-1.5" />
              Back to Ad Management
            </Button>
          </Link>
        }
      />

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Device Preview</CardTitle>
          <CardDescription>
            Select a device type to see how ad placements would appear
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="w-40">
              <Select
                id="device-type"
                value={deviceType}
                onChange={(e) => setDeviceType(e.target.value as 'desktop' | 'tablet' | 'mobile')}
              >
                <option value="desktop">Desktop</option>
                <option value="tablet">Tablet</option>
                <option value="mobile">Mobile</option>
              </Select>
            </div>
            <div className="text-sm text-gray-500">
              Current view: <span className="font-medium text-gray-700">{deviceType}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Simulated Fademail Interface with Side Rail Ads */}
      <div className="relative">
        {/* Left Rail Ad - Only visible on desktop */}
        {deviceType === 'desktop' && (
          <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-[170px]">
            <AdPlaceholder
              placementId="left-rail"
              height="600px"
              width="160px"
              description="Left rail ad - sticks to the left side of the screen as user scrolls"
              deviceTypes={['desktop']}
            />
          </div>
        )}

        {/* Right Rail Ad - Only visible on desktop */}
        {deviceType === 'desktop' && (
          <div className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-[170px]">
            <AdPlaceholder
              placementId="right-rail"
              height="600px"
              width="160px"
              description="Right rail ad - sticks to the right side of the screen as user scrolls"
              deviceTypes={['desktop']}
            />
          </div>
        )}

        {/* Main Content */}
        <div className={`mx-auto ${getContainerWidth()} bg-white shadow-md rounded-lg overflow-hidden`}>
          {/* Navbar */}
          <div className="bg-indigo-600 text-white p-4 flex justify-between items-center">
            <div className="font-bold text-xl">Fademail</div>
            <div className="flex space-x-2">
              <div className="w-8 h-8 bg-indigo-500 rounded-full"></div>
            </div>
          </div>

        {/* Header Ad Placement */}
        <div className="p-4">
          <AdPlaceholder
            placementId="header"
            height="90px"
            description="Header ad - appears below the navbar, full width"
            deviceTypes={['desktop']}
          />
        </div>

        {/* Top Ad Placement - Currently Implemented */}
        <div className="p-4 bg-green-50 border border-green-200 rounded-md mx-4 mb-4">
          <div className="text-sm font-medium text-green-700 mb-2">Currently Implemented:</div>
          <AdPlaceholder
            placementId="top"
            height="90px"
            description="Top ad - between navbar and email controls"
            deviceTypes={['desktop', 'tablet', 'mobile']}
          />
        </div>

        {/* Email Controls */}
        <div className="bg-gray-50 p-4 mx-4 rounded-md mb-4">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <div className="flex-1">
              <div className="font-medium text-gray-700 mb-1">Email Address:</div>
              <div className="bg-white border border-gray-300 rounded p-2 flex justify-between">
                <span className="text-gray-500"><EMAIL></span>
                <button className="text-indigo-600 text-sm">Copy</button>
              </div>
            </div>
            <div>
              <button className="bg-indigo-600 text-white px-4 py-2 rounded">New Address</button>
            </div>
          </div>
          <div className="mt-2 text-sm text-gray-500">
            Expires in: 29:45
          </div>
        </div>

        {/* Middle Ad Placement - Currently Implemented */}
        <div className="p-4 bg-green-50 border border-green-200 rounded-md mx-4 mb-4">
          <div className="text-sm font-medium text-green-700 mb-2">Currently Implemented:</div>
          <AdPlaceholder
            placementId="middle"
            height="90px"
            description="Middle ad - between email controls and inbox/viewer"
            deviceTypes={['desktop', 'tablet']}
          />
        </div>

        {/* Main Content Area */}
        <div className="flex flex-col md:flex-row mx-4 mb-4 gap-4">
          {/* Sidebar */}
          <div className="md:w-1/3 flex flex-col gap-4">
            {/* Sidebar Top Ad */}
            <AdPlaceholder
              placementId="sidebar-top"
              height="250px"
              description="Sidebar top ad - above the inbox"
              deviceTypes={['desktop', 'tablet']}
            />

            {/* Inbox */}
            <div className="bg-gray-50 rounded-md p-4 flex-1">
              <div className="font-medium text-gray-700 mb-2">Inbox</div>
              {[1, 2, 3].map((i) => (
                <div key={i} className="bg-white p-2 rounded mb-2 border border-gray-200">
                  <div className="font-medium">Email Subject {i}</div>
                  <div className="text-sm text-gray-500">From: sender{i}@example.com</div>
                </div>
              ))}
            </div>

            {/* Sidebar Bottom Ad */}
            <AdPlaceholder
              placementId="sidebar-bottom"
              height="250px"
              description="Sidebar bottom ad - below the inbox"
              deviceTypes={['desktop', 'tablet']}
            />

            {/* Inbox Bottom Ad */}
            <AdPlaceholder
              placementId="inbox-bottom"
              height="90px"
              description="Inbox bottom ad - below the inbox"
              deviceTypes={['desktop', 'tablet', 'mobile']}
            />
          </div>

          {/* Email Viewer */}
          <div className="md:w-2/3 bg-gray-50 rounded-md p-4 flex flex-col">
            <div className="font-medium text-gray-700 mb-2">Email Viewer</div>
            <div className="bg-white p-4 rounded border border-gray-200 flex-1">
              <div className="font-medium mb-2">Email Subject 1</div>
              <div className="text-sm text-gray-500 mb-4">From: <EMAIL></div>
              <div className="text-gray-700">
                This is the email content. It would display the full email message here.
              </div>
            </div>

            {/* Email View Right Ad */}
            {deviceType === 'desktop' && (
              <div className="absolute right-4 mt-16 ml-4" style={{ width: '160px' }}>
                <AdPlaceholder
                  placementId="email-view-right"
                  height="600px"
                  width="160px"
                  description="Email view right ad - appears to the right of the email content"
                  deviceTypes={['desktop']}
                />
              </div>
            )}
          </div>
        </div>

        {/* Footer Ad */}
        <div className="p-4 mb-4 mx-4">
          <AdPlaceholder
            placementId="footer"
            height="90px"
            description="Footer ad - appears at the bottom of the page"
            deviceTypes={['desktop', 'tablet', 'mobile']}
          />
        </div>

        {/* Footer */}
        <div className="bg-gray-100 p-4 text-center text-sm text-gray-500">
          © 2024 Fademail - Temporary Email Service
        </div>
        </div>
      </div>

      {/* Legend */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Ad Placement Legend</CardTitle>
          <CardDescription>
            Summary of all available ad placements and their properties
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Placement ID
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Location
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Device Types
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">top</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Between navbar and email controls</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Desktop, Tablet, Mobile</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                      Implemented
                    </span>
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">middle</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Between email controls and inbox/viewer</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Desktop, Tablet</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                      Implemented
                    </span>
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">sidebar-top</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Above the inbox</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Desktop, Tablet</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                      Not Implemented
                    </span>
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">sidebar-bottom</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Below the inbox</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Desktop, Tablet</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                      Not Implemented
                    </span>
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">inbox-bottom</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Below the inbox</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Desktop, Tablet, Mobile</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                      Not Implemented
                    </span>
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">email-view-right</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Right side of email viewer</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Desktop only</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                      Not Implemented
                    </span>
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">header</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Below the navbar</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Desktop only</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                      Not Implemented
                    </span>
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">footer</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Bottom of the page</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Desktop, Tablet, Mobile</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                      Not Implemented
                    </span>
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">left-rail</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Sticks to the left side of the screen</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Desktop (large screens only)</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                      Implemented
                    </span>
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">right-rail</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Sticks to the right side of the screen</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Desktop (large screens only)</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                      Implemented
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
