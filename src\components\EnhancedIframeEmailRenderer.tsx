'use client';

import React, { useEffect, useState, useRef, useCallback } from 'react';
import { useIframeHeight } from '@/hooks/useIframeHeight';
import { useIframeScaling } from '@/hooks/useIframeScaling';
import { useImageLoading } from '@/hooks/useImageLoading';
import { useIframeContent } from '@/hooks/useIframeContent';
import { useIframeMessage } from '@/hooks/useIframeMessage';
import { IframeControls } from '@/components/email/IframeControls';
import { ImageLoadingIndicator } from '@/components/email/ImageLoadingIndicator';
import { LoadingSkeleton } from '@/components/email/LoadingSkeleton';

interface EnhancedIframeEmailRendererProps {
  /**
   * The HTML content to render in the iframe
   */
  html: string;

  /**
   * Optional className for the iframe container
   */
  className?: string;

  /**
   * Optional style object for the iframe
   */
  style?: React.CSSProperties;

  /**
   * Optional array of web fonts to load in the iframe
   * Each font should be a Google Fonts URL or similar
   */
  webFonts?: string[];

  /**
   * Optional scale factor for the email content (0.5 to 2.0)
   * Default is 1.0 (100%)
   */
  scaleFactor?: number;

  /**
   * Optional email ID to identify guide emails
   * Guide emails have IDs that start with "guide-"
   */
  emailId?: string;
}

/**
 * EnhancedIframeEmailRenderer Component
 *
 * An improved component for rendering email HTML content in an iframe.
 * Features:
 * - Complete style isolation through iframe
 * - Automatic height adjustment
 * - Server-side rendering compatibility
 * - Smooth scaling/zooming with transitions
 * - Image loading tracking
 * - Print functionality
 * - Modular architecture with custom hooks
 */
export default function EnhancedIframeEmailRenderer({
  html,
  className = '',
  style = {},
  webFonts = [],
  scaleFactor = 1.0,
  emailId = ''
}: EnhancedIframeEmailRendererProps) {
  // Check if this is a guide email
  const isGuideEmail = emailId?.startsWith('guide-') || false;

  // Create a ref for the iframe
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // State to track if the component is mounted (for SSR compatibility)
  const [isMounted, setIsMounted] = useState(false);

  // State to track loading status - set to false immediately for guide emails
  const [isLoading, setIsLoading] = useState(!isGuideEmail);

  // Use custom hooks for functionality - with optimizations for guide emails
  const { scaleFactor: currentScaleFactor, setScale, zoomIn, zoomOut } =
    useIframeScaling({ initialScale: scaleFactor });

  // Use a larger initial height for guide emails to reduce layout shifts
  const { height, handleHeightMessage, resetHeight } =
    useIframeHeight(isGuideEmail ? 400 : 200);

  // Skip image loading tracking for guide emails since they don't have external images
  const {
    totalImages,
    loadedImages,
    imageLoadingProgress,
    handleTotalImages,
    handleImagesLoaded,
    resetImageLoading
  } = useImageLoading();

  // Generate HTML content for the iframe
  // For guide emails, we can use a simpler approach with fewer transformations
  const { htmlContent } = useIframeContent({
    html,
    webFonts,
    scaleFactor: currentScaleFactor,
    isGuideEmail
  });

  // Effect to handle client-side rendering - immediate for guide emails
  useEffect(() => {
    if (isGuideEmail) {
      // For guide emails, mount immediately without delay
      setIsMounted(true);
    } else {
      // For regular emails, use setTimeout to ensure we're in the browser environment
      const timeoutId = setTimeout(() => {
        setIsMounted(true);
      }, 0);
      return () => clearTimeout(timeoutId);
    }
  }, [isGuideEmail]);

  // Simple message handler for iframe communication
  const handleMessage = useCallback((event: MessageEvent) => {
    if (!event.data || !event.data.type) return;

    switch (event.data.type) {
      case 'resize-iframe':
        // Handle height adjustment message
        const newHeight = handleHeightMessage(
          event.data.height,
          event.data.isScaleChange
        );

        // If we have a valid height, update loading state
        if (newHeight > 0) {
          setIsLoading(false);
        }
        break;

      case 'total-images':
        // Skip image tracking for guide emails
        if (!isGuideEmail) {
          handleTotalImages(event.data.count);
        }
        break;

      case 'images-loaded':
        // Skip image tracking for guide emails
        if (!isGuideEmail) {
          handleImagesLoaded(event.data.count, event.data.total);
        }
        break;
    }
  }, [handleHeightMessage, handleTotalImages, handleImagesLoaded, isGuideEmail]);

  // Set up message handling
  const { sendMessage } = useIframeMessage(iframeRef, handleMessage);

  // Simple function to send scale change to iframe
  const handleSetScale = useCallback((newScale: number) => {
    // Get bounded scale value
    const boundedScale = setScale(newScale);

    // Send the scale change message directly to the iframe
    sendMessage({ type: 'set-scale', scale: boundedScale });

    return boundedScale;
  }, [sendMessage, setScale]);

  // Function to reset zoom to 100%
  const resetZoom = useCallback(() => {
    handleSetScale(1.0); // Reset to 100%
  }, [handleSetScale]);

  // Effect to handle iframe initialization and content
  useEffect(() => {
    if (!isMounted) return;

    // For guide emails, we can skip some initialization steps
    if (isGuideEmail) {
      // Just reset height for guide emails
      resetHeight();
    } else {
      // Reset states when content changes for regular emails
      resetHeight();
      resetImageLoading();
    }
  }, [isMounted, html, resetHeight, resetImageLoading, isGuideEmail]);

  // Effect to update scale when scaleFactor prop changes
  useEffect(() => {
    if (!isMounted) return;

    // Only update if the scale has actually changed
    if (Math.abs(scaleFactor - currentScaleFactor) > 0.01) {
      console.log(`Updating scale from ${currentScaleFactor} to ${scaleFactor}`);
      handleSetScale(scaleFactor);
    }
  }, [scaleFactor, currentScaleFactor, handleSetScale, isMounted]);

  return (
    <div
      className={`enhanced-iframe-container ${className}`}
      style={{
        width: '100%',
        overflowY: 'visible', // Allow content to expand vertically
        overflowX: 'auto', // Allow horizontal scrolling when needed
        position: 'relative',
        transition: 'opacity 0.3s ease-in-out'
      }}
    >
      {/* Image loading indicator - only show for non-guide emails */}
      {!isGuideEmail && (
        <div className="flex items-center mb-3 px-1">
          <ImageLoadingIndicator
            totalImages={totalImages}
            loadedImages={loadedImages}
            progress={imageLoadingProgress}
            className="flex-1"
          />
        </div>
      )}

      {/* Always render the iframe, but hide it until loaded */}
      <iframe
        ref={iframeRef}
        title="Email content"
        style={{
          width: '100%',
          border: 'none',
          overflowY: 'hidden', // Hide vertical scrollbar
          overflowX: 'auto', // Allow horizontal scrolling
          // scrolling is an HTML attribute, not a CSS property
          height: `${height}px`,
          minHeight: isGuideEmail ? '400px' : '200px', // Higher minimum height for guide emails
          display: 'block',
          // Simple transitions for height and opacity
          transition: 'height 0.3s ease-out, opacity 0.3s ease-out',
          // Always show when mounted, don't wait for loading
          opacity: isMounted ? 1 : 0,
          ...style
        }}
        scrolling="no" // HTML attribute to disable scrolling (for older browsers)
        sandbox="allow-scripts allow-popups allow-popups-to-escape-sandbox"
        srcDoc={htmlContent}
      />
    </div>
  );
}
