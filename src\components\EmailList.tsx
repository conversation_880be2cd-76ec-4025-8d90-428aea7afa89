'use client';

import { useState, useEffect } from 'react';
import { Email } from '@/lib/emailProcessing';
import EmailSkeleton from './EmailSkeleton';
import { UI } from '@/lib/constants';
import CountdownTimer from './CountdownTimer';

interface EmailListProps {
  /** List of emails to display */
  emails: Email[];

  /** ID of the currently selected email */
  selectedEmailId: string | null;

  /** Whether emails are currently loading */
  isLoading: boolean;

  /** Whether a refresh is in progress */
  isRefreshing: boolean;

  /** Last time the emails were refreshed */
  lastRefreshed: string;

  /** Whether real-time updates are active */
  isRealtimeActive?: boolean;

  /** Whether auto-refresh is disabled */
  disableAutoRefresh?: boolean;

  /** Whether a new address is being generated */
  isGeneratingAddress?: boolean;

  /** Expiration date of the email address */
  expirationDate?: Date | null;

  /** Whether guide emails are being shown */
  showingGuideEmails?: boolean;

  /** Whether we're in an address transition */
  inAddressTransition?: boolean;

  /** Function to select an email */
  onSelectEmail: (emailId: string, isDirectClick?: boolean) => void;

  /** Function to delete an email */
  onDeleteEmail: (emailId: string, e: React.MouseEvent) => void;

  /** Function to refresh the inbox */
  onRefreshInbox: () => void;

  /** Whether to show the inbox on mobile */
  showMobileInbox: boolean;
}

/**
 * EmailList Component
 *
 * Displays the list of emails in the inbox. Handles email selection, deletion,
 * and refresh functionality. Adapts to mobile and desktop views.
 *
 * @param {Email[]} props.emails - The list of emails to display
 * @param {string|null} props.selectedEmailId - The ID of the currently selected email
 * @param {boolean} props.isLoading - Whether emails are currently loading
 * @param {boolean} props.isRefreshing - Whether a refresh is in progress
 * @param {string} props.lastRefreshed - Last time the emails were refreshed
 * @param {Function} props.onSelectEmail - Function to select an email
 * @param {Function} props.onDeleteEmail - Function to delete an email
 * @param {Function} props.onRefreshInbox - Function to refresh the inbox
 * @param {boolean} props.showMobileInbox - Whether to show the inbox on mobile
 * @returns {JSX.Element} The rendered component
 */
export default function EmailList({
  emails,
  selectedEmailId,
  isLoading,
  isRefreshing,
  lastRefreshed,
  isRealtimeActive,
  disableAutoRefresh,
  isGeneratingAddress,
  expirationDate,
  showingGuideEmails = false,
  inAddressTransition = false,
  onSelectEmail,
  onDeleteEmail,
  onRefreshInbox,
  showMobileInbox
}: EmailListProps) {

  return (
    <div className={`w-full md:w-1/3 lg:w-1/3 bg-white overflow-y-auto rounded-md md:rounded-r-none transition-all duration-300 ease-in-out shadow-sm ${!showMobileInbox ? 'hidden md:block' : 'block'}`}>
      {/* Mobile Indicator - Only visible on small screens */}
      <div className="md:hidden bg-neutral-100 py-2.5 px-4 text-neutral-800 text-sm font-medium border-b border-neutral-200">
        <div className="flex items-center justify-between">
          <div className="w-4"></div> {/* Empty div for spacing */}
          {selectedEmailId && (
            <button
              onClick={() => onSelectEmail(selectedEmailId, true)}
              className="text-[#605f5f] flex items-center hover:text-[#505050] transition-colors"
            >
              <span>View Email</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          )}
        </div>
      </div>
      <div className="border-b border-neutral-100 sticky top-0 bg-white z-10 inbox-header backdrop-blur-sm bg-opacity-95">
        <div className="p-4 flex justify-between items-center">
          <h2 className="text-base font-semibold text-neutral-800 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-neutral-800" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6z" />
              <path d="M22 6l-10 7L2 6" />
            </svg>
            Inbox
          </h2>

          {/* Refresh Button */}
          <button
            onClick={onRefreshInbox}
            disabled={isRefreshing}
            className={`p-1.5 rounded-full hover:bg-neutral-200 transition-all duration-200 text-[#605f5f] focus:outline-none focus:ring-2 focus:ring-[#605f5f] active:scale-95 ${isRefreshing ? 'cursor-not-allowed opacity-70' : ''}`}
            aria-label="Refresh inbox"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className={`h-4.5 w-4.5 ${isRefreshing ? 'animate-spin' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>

        {/* Last refreshed time and auto-refresh status */}
        {lastRefreshed && (
          <div className="px-4 pb-2 -mt-1 flex justify-between items-center">
            <p className="text-xs text-neutral-500">
              Last refreshed: {lastRefreshed}
            </p>
            <div className="flex items-center">
              {/* Only show auto-refresh status when not showing guide emails */}
              {!showingGuideEmails && (
                <>
                  {disableAutoRefresh ? (
                    <div className="flex items-center text-xs text-warning-500">
                      <span className="h-1.5 w-1.5 bg-warning-500 rounded-full mr-1 animate-pulse"></span>
                      <span>Auto-refresh paused</span>
                    </div>
                  ) : (
                    <CountdownTimer
                      expirationDate={expirationDate}
                      show={!!lastRefreshed && !disableAutoRefresh && !!expirationDate}
                    />
                  )}
                </>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Email List */}
      <div className="divide-y" style={{ borderColor: 'var(--earth-beige-secondary)' }}>
        {inAddressTransition ? (
          // Show a special transition state during address generation
          <AddressTransitionState />
        ) : emails.length === 0 ? (
          <EmptyInbox />
        ) : (
          emails.map((email) => (
            <EmailListItem
              key={email.id}
              email={email}
              isSelected={email.id === selectedEmailId}
              onSelect={() => onSelectEmail(email.id, true)}
              onDelete={(e) => onDeleteEmail(email.id, e)}
            />
          ))
        )}
      </div>
    </div>
  );
}

interface EmailListItemProps {
  /** Email to display */
  email: Email;

  /** Whether this email is selected */
  isSelected: boolean;

  /** Function to call when the email is selected */
  onSelect: () => void;

  /** Function to call when the email is deleted */
  onDelete: (e: React.MouseEvent) => void;
}

/**
 * EmailListItem Component
 *
 * Displays a single email in the inbox list
 */
function EmailListItem({ email, isSelected, onSelect, onDelete }: EmailListItemProps) {
  return (
    <div
      onKeyDown={(e) => {
        // Handle keyboard navigation
        if (e.key === 'Enter' || e.key === ' ') {
          onSelect();
          e.preventDefault();
        }
      }}
      role="button"
      tabIndex={0}
      aria-selected={isSelected}
      className={`p-3.5 sm:p-4 cursor-pointer hover:bg-neutral-50 transition-all duration-200 ease-in-out rounded-md my-1 sm:my-1.5 mx-1.5 ${
        isSelected
          ? `bg-neutral-100 border-l-4 border-b border-l-[#ce601c] border-b-[#ce601c] shadow-sm animate-email-select will-change-transform`
          : !email.isRead
            ? 'bg-white shadow-sm border-b border-l animate-email-deselect will-change-transform'
            : 'bg-white shadow-sm border-b border-l animate-email-deselect will-change-transform'
      }`}
      style={{
        borderColor: isSelected ? undefined : 'var(--earth-beige-secondary)'
      }}
      onClick={onSelect}
    >
      <div className="flex justify-between items-start transition-transform duration-200 ease-out">
        <div className="flex items-center max-w-[70%]">
          {/* Unread indicator */}
          {!email.isRead ? (
            <div className="w-2 h-2 sm:w-2.5 sm:h-2.5 bg-[#ce601c] rounded-full mr-1.5 sm:mr-2 flex-shrink-0 animate-pulse" aria-label="Unread email"></div>
          ) : (
            <div className="w-2 h-2 sm:w-2.5 sm:h-2.5 mr-1.5 sm:mr-2 flex-shrink-0" aria-label="Read email"></div>
          )}
          <h3 className={`text-sm truncate transition-colors duration-200 ease-out ${!email.isRead ? 'font-semibold text-neutral-800' : 'font-medium text-neutral-600'}`}>{email.sender}</h3>
        </div>
        <div className="flex items-center">
          <span className="text-xs text-neutral-500 whitespace-nowrap mr-1.5 sm:mr-2 transition-opacity duration-200 ease-out">{email.timestamp}</span>
          {/* Delete button */}
          <button
            onClick={(e) => {
              e.stopPropagation(); // Prevent triggering the parent onClick
              onDelete(e);
            }}
            onKeyDown={(e) => {
              // Prevent the parent's keydown handler from being triggered
              e.stopPropagation();
            }}
            className="p-1 text-neutral-400 hover:text-error-500 transition-colors rounded-full hover:bg-neutral-100 active:scale-95"
            aria-label={`Delete email from ${email.sender}`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 transition-transform duration-200 ease-out" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        </div>
      </div>
      <p className={`text-xs sm:text-sm mt-1.5 truncate transition-colors duration-200 ease-out ${!email.isRead ? 'font-medium text-neutral-700' : 'font-normal text-neutral-600'}`}>{email.subject}</p>
      <p className={`text-xs mt-1 truncate transition-colors duration-200 ease-out ${!email.isRead ? 'text-neutral-600' : 'text-neutral-500'}`}>{email.preview}</p>
      {/* Attachment indicator */}
      {email.attachments.length > 0 && (
        <div className="flex items-center mt-2 text-2xs sm:text-xs text-neutral-500 transition-opacity duration-200 ease-out">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 transition-transform duration-200 ease-out" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
          </svg>
          <span>{email.attachments.length} {email.attachments.length === 1 ? 'attachment' : 'attachments'}</span>
        </div>
      )}
    </div>
  );
}

/**
 * AddressTransitionState Component
 *
 * Displays a message during address transition
 */
function AddressTransitionState() {
  return (
    <div className="p-8 text-center text-neutral-500 flex flex-col items-center justify-center h-64">
      <div className="relative mb-6">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-14 w-14 text-neutral-200 animate-pulse" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
        <div className="absolute -bottom-1 -right-1 bg-neutral-100 text-[#605f5f] rounded-full p-1.5 animate-spin">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </div>
      </div>
      <p className="text-sm">Generating new address...</p>
      <p className="text-xs text-neutral-400 mt-2">Please wait while we set up your new inbox</p>
    </div>
  );
}

/**
 * EmptyInbox Component
 *
 * Displays a message when the inbox is empty
 */
function EmptyInbox() {
  return (
    <div className="p-8 text-center text-neutral-500 flex flex-col items-center justify-center h-64">
      <div className="relative mb-6">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-14 w-14 text-neutral-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
        <div className="absolute -bottom-1 -right-1 bg-neutral-100 text-[#605f5f] rounded-full p-1.5">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        </div>
      </div>
      <p className="text-sm">No emails yet. Waiting for new messages...</p>
      <p className="text-xs text-neutral-400 mt-2">Messages will appear here as they arrive</p>
    </div>
  );
}
