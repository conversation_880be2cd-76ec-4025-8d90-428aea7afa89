/**
 * SMTP Test Mode Selector Component
 * 
 * Component for selecting between auto and custom test modes
 */

'use client';

import React from 'react';

interface SmtpTestModeSelectorProps {
  selectedMode: 'auto' | 'custom';
  onModeChange: (mode: 'auto' | 'custom') => void;
  disabled?: boolean;
}

export default function SmtpTestModeSelector({ 
  selectedMode, 
  onModeChange, 
  disabled = false 
}: SmtpTestModeSelectorProps) {
  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-lg border border-[#4a3728]/20 p-4">
      <div className="flex items-center gap-3 mb-4">
        <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-[#956b50] to-[#4a3728] flex items-center justify-center">
          <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
          </svg>
        </div>
        <div>
          <h2 className="text-xl font-bold" style={{ color: '#1b130e' }}>Test Mode</h2>
          <p className="text-sm" style={{ color: '#4a3728' }}>Choose how you want to test your SMTP configuration</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Auto Mode */}
        <div 
          className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
            selectedMode === 'auto' 
              ? 'border-[#66b077] bg-[#66b077]/5' 
              : 'border-[#4a3728]/20 hover:border-[#66b077]/50'
          } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
          onClick={() => !disabled && onModeChange('auto')}
        >
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 mt-1">
              <input
                type="radio"
                name="testMode"
                value="auto"
                checked={selectedMode === 'auto'}
                onChange={() => onModeChange('auto')}
                disabled={disabled}
                className="w-4 h-4 text-[#66b077] focus:ring-[#66b077] border-[#4a3728]/30"
              />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <h3 className="font-semibold" style={{ color: '#1b130e' }}>Auto-Generated Email + Analysis</h3>
                <span className="px-2 py-1 text-xs font-medium bg-[#66b077] text-white rounded">Recommended</span>
              </div>
              <p className="text-sm mb-3" style={{ color: '#4a3728' }}>
                Automatically generates a test email address and performs comprehensive email authentication analysis.
              </p>
              
              <div className="space-y-2">
                <h4 className="text-sm font-medium" style={{ color: '#1b130e' }}>What this mode does:</h4>
                <ul className="space-y-1 text-sm" style={{ color: '#4a3728' }}>
                  <li className="flex items-center gap-2">
                    <svg className="w-4 h-4 flex-shrink-0" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Tests SMTP connectivity
                  </li>
                  <li className="flex items-center gap-2">
                    <svg className="w-4 h-4 flex-shrink-0" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Validates SPF records
                  </li>
                  <li className="flex items-center gap-2">
                    <svg className="w-4 h-4 flex-shrink-0" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Checks DKIM signatures
                  </li>
                  <li className="flex items-center gap-2">
                    <svg className="w-4 h-4 flex-shrink-0" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Analyzes DMARC compliance
                  </li>
                  <li className="flex items-center gap-2">
                    <svg className="w-4 h-4 flex-shrink-0" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Provides improvement recommendations
                  </li>
                </ul>
              </div>

              <div className="mt-3 p-2 rounded" style={{ backgroundColor: '#f0f9ff' }}>
                <p className="text-xs" style={{ color: '#4a3728' }}>
                  <strong>Best for:</strong> Comprehensive email deliverability testing and authentication setup validation.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Custom Mode */}
        <div 
          className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
            selectedMode === 'custom' 
              ? 'border-[#ce601c] bg-[#ce601c]/5' 
              : 'border-[#4a3728]/20 hover:border-[#ce601c]/50'
          } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
          onClick={() => !disabled && onModeChange('custom')}
        >
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 mt-1">
              <input
                type="radio"
                name="testMode"
                value="custom"
                checked={selectedMode === 'custom'}
                onChange={() => onModeChange('custom')}
                disabled={disabled}
                className="w-4 h-4 text-[#ce601c] focus:ring-[#ce601c] border-[#4a3728]/30"
              />
            </div>
            <div className="flex-1">
              <h3 className="font-semibold mb-2" style={{ color: '#1b130e' }}>Custom Recipient</h3>
              <p className="text-sm mb-3" style={{ color: '#4a3728' }}>
                Send a test email to your specified recipient address to verify SMTP connectivity only.
              </p>
              
              <div className="space-y-2">
                <h4 className="text-sm font-medium" style={{ color: '#1b130e' }}>What this mode does:</h4>
                <ul className="space-y-1 text-sm" style={{ color: '#4a3728' }}>
                  <li className="flex items-center gap-2">
                    <svg className="w-4 h-4 flex-shrink-0" style={{ color: '#ce601c' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Tests SMTP connectivity
                  </li>
                  <li className="flex items-center gap-2">
                    <svg className="w-4 h-4 flex-shrink-0" style={{ color: '#ce601c' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Confirms email delivery
                  </li>
                  <li className="flex items-center gap-2">
                    <svg className="w-4 h-4 flex-shrink-0" style={{ color: '#ce601c' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Provides diagnostic logs
                  </li>
                </ul>
              </div>

              <div className="mt-3 p-2 rounded" style={{ backgroundColor: '#fffbeb' }}>
                <p className="text-xs" style={{ color: '#4a3728' }}>
                  <strong>Best for:</strong> Quick SMTP connectivity testing with real email addresses.
                </p>
              </div>

              <div className="mt-2 p-2 rounded border border-[#f59e0b]/30" style={{ backgroundColor: '#fffbeb' }}>
                <div className="flex items-start gap-2">
                  <svg className="w-4 h-4 mt-0.5 flex-shrink-0" style={{ color: '#f59e0b' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  <p className="text-xs" style={{ color: '#4a3728' }}>
                    <strong>Note:</strong> This mode does not include email authentication analysis (SPF, DKIM, DMARC).
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mode Comparison */}
      <div className="mt-6 p-4 rounded-lg" style={{ backgroundColor: '#f3ece8' }}>
        <h3 className="font-semibold mb-3" style={{ color: '#1b130e' }}>Quick Comparison</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-[#4a3728]/20">
                <th className="text-left py-2" style={{ color: '#1b130e' }}>Feature</th>
                <th className="text-center py-2" style={{ color: '#1b130e' }}>Auto Mode</th>
                <th className="text-center py-2" style={{ color: '#1b130e' }}>Custom Mode</th>
              </tr>
            </thead>
            <tbody className="space-y-1">
              <tr>
                <td className="py-1" style={{ color: '#4a3728' }}>SMTP Testing</td>
                <td className="text-center py-1">
                  <svg className="w-4 h-4 mx-auto" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </td>
                <td className="text-center py-1">
                  <svg className="w-4 h-4 mx-auto" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </td>
              </tr>
              <tr>
                <td className="py-1" style={{ color: '#4a3728' }}>Authentication Analysis</td>
                <td className="text-center py-1">
                  <svg className="w-4 h-4 mx-auto" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </td>
                <td className="text-center py-1">
                  <svg className="w-4 h-4 mx-auto" style={{ color: '#956b50' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </td>
              </tr>
              <tr>
                <td className="py-1" style={{ color: '#4a3728' }}>Custom Recipient</td>
                <td className="text-center py-1">
                  <svg className="w-4 h-4 mx-auto" style={{ color: '#956b50' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </td>
                <td className="text-center py-1">
                  <svg className="w-4 h-4 mx-auto" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </td>
              </tr>
              <tr>
                <td className="py-1" style={{ color: '#4a3728' }}>Test Duration</td>
                <td className="text-center py-1 text-xs" style={{ color: '#4a3728' }}>10-30s</td>
                <td className="text-center py-1 text-xs" style={{ color: '#4a3728' }}>5-15s</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
