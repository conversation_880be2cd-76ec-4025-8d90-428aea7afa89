'use client';

import { useState, useEffect } from 'react';
import { formatDistanceToNow } from 'date-fns';
import { ArrowPathIcon, CheckCircleIcon, ExclamationCircleIcon, XCircleIcon } from '@heroicons/react/24/solid';
import { <PERSON>, <PERSON>Header, CardT<PERSON>le, Card<PERSON>ontent, CardFooter } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Spinner } from '@/components/ui/Spinner';
import { Badge } from '@/components/ui/Badge';
import { Checkbox } from '@/components/ui/Checkbox';

interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  components: {
    supabase: {
      status: 'healthy' | 'degraded' | 'unhealthy';
      responseTime?: number;
      message?: string;
    };
    guerrilla: {
      status: 'healthy' | 'degraded' | 'unhealthy';
      responseTime?: number;
      message?: string;
    };
    emailService: {
      status: 'healthy' | 'degraded' | 'unhealthy';
      message?: string;
    };
    config: {
      status: 'healthy' | 'degraded' | 'unhealthy';
      message?: string;
    };
  };
  metrics: {
    activeEmails: number;
    emailsReceivedLast24Hours: number;
    totalLogs: number;
    errorLogs: number;
    activeDomains?: number;
  };
  system?: {
    uptime: number;
    memory: {
      total: number;
      free: number;
      used: number;
    };
    cpu: {
      cores: number;
      load: number[];
    };
  };
}

export default function HealthDashboard() {
  const [healthData, setHealthData] = useState<HealthStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds

  // Fetch health data
  const fetchHealthData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/management-portal-x7z9y2/health');

      if (!response.ok) {
        throw new Error(`Failed to fetch health data: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setHealthData(data.data);
        setLastUpdated(new Date());
      } else {
        setError(data.error || 'Failed to fetch health data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while fetching health data');
      console.error('Error fetching health data:', err);
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchHealthData();
  }, []);

  // Auto-refresh
  useEffect(() => {
    if (!autoRefresh) return;

    const intervalId = setInterval(() => {
      fetchHealthData();
    }, refreshInterval * 1000);

    return () => clearInterval(intervalId);
  }, [autoRefresh, refreshInterval]);

  // Format bytes to human-readable format
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format seconds to human-readable format
  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    return `${days}d ${hours}h ${minutes}m ${secs}s`;
  };

  // Get status icon
  const getStatusIcon = (status: 'healthy' | 'degraded' | 'unhealthy') => {
    switch (status) {
      case 'healthy':
        return <CheckCircleIcon className="h-5 w-5 text-[#28c08e]" />;
      case 'degraded':
        return <ExclamationCircleIcon className="h-5 w-5 text-[#f59e0b]" />;
      case 'unhealthy':
        return <XCircleIcon className="h-5 w-5 text-[#f15757]" />;
      default:
        return null;
    }
  };

  // Get status color
  const getStatusColor = (status: 'healthy' | 'degraded' | 'unhealthy') => {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'degraded':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'unhealthy':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0 mb-6 bg-white p-4 rounded-lg shadow-sm">
        <div className="flex items-center">
          <div className="flex items-center justify-center h-8 w-8 mr-3 text-indigo-500 flex-shrink-0">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="currentColor">
              <path fillRule="evenodd" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zM12.75 6a.75.75 0 00-1.5 0v6c0 .414.336.75.75.75h4.5a.75.75 0 000-1.5h-3.75V6z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              System Health
            </h2>
            {lastUpdated && (
              <p className="text-sm text-gray-500 mt-1">
                Last updated: {formatDistanceToNow(lastUpdated, { addSuffix: true })}
              </p>
            )}
          </div>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Checkbox
              id="auto-refresh"
              checked={autoRefresh}
              onCheckedChange={(checked) => setAutoRefresh(checked)}
              label="Auto-refresh"
            />
          </div>
          <div className="flex items-center gap-2">
            <label htmlFor="refresh-interval" className="text-sm text-gray-700 whitespace-nowrap">
              Interval (s):
            </label>
            <select
              id="refresh-interval"
              value={refreshInterval}
              onChange={(e) => setRefreshInterval(Number(e.target.value))}
              className="rounded border-gray-300 text-sm focus:ring-indigo-500 focus:border-indigo-500 p-1 w-16"
              disabled={!autoRefresh}
            >
              <option value="10">10</option>
              <option value="30">30</option>
              <option value="60">60</option>
              <option value="300">300</option>
            </select>
          </div>
          <Button
            variant="primary"
            size="sm"
            onClick={fetchHealthData}
            disabled={loading}
            isLoading={loading}
            leftIcon={!loading ? <ArrowPathIcon className="h-4 w-4" /> : undefined}
          >
            Refresh
          </Button>
        </div>
      </div>
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6 animate-slideIn">
          <div className="flex items-start">
            <XCircleIcon className="h-5 w-5 text-red-500 mr-2 mt-0.5" />
            <div>
              <p className="text-red-700 font-medium">Error</p>
              <p className="text-red-600">{error}</p>
              <Button
                variant="ghost"
                size="sm"
                className="mt-2 text-red-600 p-0 h-auto"
                onClick={() => setError(null)}
              >
                Dismiss
              </Button>
            </div>
          </div>
        </div>
      )}

      {loading && !healthData ? (
        <div className="flex flex-col justify-center items-center h-64">
          <Spinner size="lg" variant="primary" className="mb-4" />
          <p className="text-gray-500">Loading health data...</p>
        </div>
      ) : healthData ? (
        <div className="space-y-6">
          {/* Overall Status */}
          <Card className="hover:shadow-md transition-all duration-300">
            <CardHeader className="flex flex-row justify-between items-center">
              <CardTitle className="text-lg font-medium text-gray-900 flex items-center">
                <div className="flex items-center justify-center h-6 w-6 mr-2 text-indigo-500 flex-shrink-0">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
                    <path fillRule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clipRule="evenodd" />
                  </svg>
                </div>
                Overall Status
              </CardTitle>
              <Badge
                variant={healthData.status === 'healthy' ? 'success' : healthData.status === 'degraded' ? 'warning' : 'danger'}
                className="capitalize flex items-center space-x-1.5 px-3 py-1"
              >
                {getStatusIcon(healthData.status)}
                <span>{healthData.status}</span>
              </Badge>
            </CardHeader>
            <CardContent>
              <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-3">
                <div className="sm:col-span-1 bg-gray-50 p-3 rounded-lg hover:shadow-sm transition-all duration-300">
                  <dt className="text-sm font-medium text-gray-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                    Version
                  </dt>
                  <dd className="mt-1 text-sm text-gray-900 font-medium">{healthData.version}</dd>
                </div>
                <div className="sm:col-span-1 bg-gray-50 p-3 rounded-lg hover:shadow-sm transition-all duration-300">
                  <dt className="text-sm font-medium text-gray-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                    </svg>
                    Timestamp
                  </dt>
                  <dd className="mt-1 text-sm text-gray-900 font-medium">
                    {new Date(healthData.timestamp).toLocaleString()}
                  </dd>
                </div>
                {healthData.system && (
                  <div className="sm:col-span-1 bg-gray-50 p-3 rounded-lg hover:shadow-sm transition-all duration-300">
                    <dt className="text-sm font-medium text-gray-500 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                      </svg>
                      System Uptime
                    </dt>
                    <dd className="mt-1 text-sm text-gray-900 font-medium">
                      {formatUptime(healthData.system.uptime)}
                    </dd>
                  </div>
                )}
              </dl>
            </CardContent>
          </Card>

          {/* Components Status */}
          <Card className="hover:shadow-md transition-all duration-300">
            <CardHeader>
              <CardTitle className="text-lg font-medium text-gray-900 flex items-center">
                <div className="flex items-center justify-center h-6 w-6 mr-2 text-indigo-500 flex-shrink-0">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M11.644 1.59a.75.75 0 01.712 0l9.75 5.25a.75.75 0 010 1.32l-9.75 5.25a.75.75 0 01-.712 0l-9.75-5.25a.75.75 0 010-1.32l9.75-5.25z" />
                    <path d="M3.265 10.602l7.668 4.129a2.25 2.25 0 002.134 0l7.668-4.13 1.37.739a.75.75 0 010 1.32l-9.75 5.25a.75.75 0 01-.71 0l-9.75-5.25a.75.75 0 010-1.32l1.37-.738z" />
                    <path d="M10.933 19.231l-7.668-4.13-1.37.739a.75.75 0 000 1.32l9.75 5.25c.221.12.489.12.71 0l9.75-5.25a.75.75 0 000-1.32l-1.37-.738-7.668 4.13a2.25 2.25 0 01-2.134-.001z" />
                  </svg>
                </div>
                Components Status
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <div className="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                          </svg>
                          Component
                        </div>
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <div className="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                          Status
                        </div>
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <div className="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                          </svg>
                          Response Time
                        </div>
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <div className="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                          </svg>
                          Message
                        </div>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {Object.entries(healthData.components).map(([key, component]) => (
                      <tr key={key} className="hover:bg-gray-50 transition-colors duration-150">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 capitalize">
                          {key}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <Badge
                            variant={component.status === 'healthy' ? 'success' : component.status === 'degraded' ? 'warning' : 'danger'}
                            className="capitalize flex items-center space-x-1.5"
                          >
                            {getStatusIcon(component.status)}
                            <span>{component.status}</span>
                          </Badge>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {'responseTime' in component && component.responseTime
                            ? <span className="font-medium">{component.responseTime}ms</span>
                            : '-'}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-500 max-w-md truncate">
                          {component.message || '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          {/* Metrics */}
          <Card className="hover:shadow-md transition-all duration-300">
            <CardHeader>
              <CardTitle className="text-lg font-medium text-gray-900 flex items-center">
                <div className="flex items-center justify-center h-6 w-6 mr-2 text-indigo-500 flex-shrink-0">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M18.375 2.25c-1.035 0-1.875.84-1.875 1.875v15.75c0 1.035.84 1.875 1.875 1.875h.75c1.035 0 1.875-.84 1.875-1.875V4.125c0-1.036-.84-1.875-1.875-1.875h-.75zM9.75 8.625c0-1.036.84-1.875 1.875-1.875h.75c1.036 0 1.875.84 1.875 1.875v11.25c0 1.035-.84 1.875-1.875 1.875h-.75a1.875 1.875 0 01-1.875-1.875V8.625zM3 13.125c0-1.036.84-1.875 1.875-1.875h.75c1.036 0 1.875.84 1.875 1.875v6.75c0 1.035-.84 1.875-1.875 1.875h-.75A1.875 1.875 0 013 19.875v-6.75z" />
                  </svg>
                </div>
                Metrics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
                <div className="bg-gray-50 overflow-hidden rounded-lg p-4 hover:shadow-md transition-all duration-300">
                  <div className="flex items-center mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                    <dt className="text-sm font-medium text-gray-500 truncate">Active Email Addresses</dt>
                  </div>
                  <dd className="text-3xl font-semibold text-gray-900">
                    {healthData.metrics.activeEmails.toLocaleString()}
                  </dd>
                </div>
                <div className="bg-gray-50 overflow-hidden rounded-lg p-4 hover:shadow-md transition-all duration-300">
                  <div className="flex items-center mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5 3a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2V5a2 2 0 00-2-2H5zm0 2h10v7h-2l-1 2H8l-1-2H5V5z" clipRule="evenodd" />
                    </svg>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Emails in System</dt>
                  </div>
                  <dd className="text-3xl font-semibold text-gray-900">
                    {healthData.metrics.emailsReceivedLast24Hours.toLocaleString()}
                  </dd>
                </div>
                <div className="bg-gray-50 overflow-hidden rounded-lg p-4 hover:shadow-md transition-all duration-300">
                  <div className="flex items-center mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.56-.5-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.56.5.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.498-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.147.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z" clipRule="evenodd" />
                    </svg>
                    <dt className="text-sm font-medium text-gray-500 truncate">Active Domains</dt>
                  </div>
                  <dd className="text-3xl font-semibold text-gray-900">
                    {healthData.metrics.activeDomains?.toLocaleString() || 0}
                  </dd>
                </div>
                <div className="bg-gray-50 overflow-hidden rounded-lg p-4 hover:shadow-md transition-all duration-300">
                  <div className="flex items-center mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                    </svg>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Logs</dt>
                  </div>
                  <dd className="text-3xl font-semibold text-gray-900">
                    {healthData.metrics.totalLogs.toLocaleString()}
                  </dd>
                </div>
                <div className="bg-gray-50 overflow-hidden rounded-lg p-4 hover:shadow-md transition-all duration-300">
                  <div className="flex items-center mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    <dt className="text-sm font-medium text-gray-500 truncate">Error Logs</dt>
                  </div>
                  <dd className="text-3xl font-semibold text-gray-900">
                    {healthData.metrics.errorLogs.toLocaleString()}
                  </dd>
                </div>
                <div className="bg-gray-50 overflow-hidden rounded-lg p-4 hover:shadow-md transition-all duration-300">
                  <div className="flex items-center mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                    </svg>
                    <dt className="text-sm font-medium text-gray-500 truncate">Error Rate</dt>
                  </div>
                  <dd className="text-3xl font-semibold text-gray-900">
                    {healthData.metrics.totalLogs > 0
                      ? `${((healthData.metrics.errorLogs / healthData.metrics.totalLogs) * 100).toFixed(2)}%`
                      : '0%'}
                  </dd>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* System Information */}
          {healthData.system && (
            <Card className="hover:shadow-md transition-all duration-300">
              <CardHeader>
                <CardTitle className="text-lg font-medium text-gray-900 flex items-center">
                  <div className="flex items-center justify-center h-6 w-6 mr-2 text-indigo-500 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
                      <path fillRule="evenodd" d="M2.25 5.25a3 3 0 013-3h13.5a3 3 0 013 3V15a3 3 0 01-3 3h-3v.257c0 .597.237 1.17.659 1.591l.621.622a.75.75 0 01-.53 1.28h-9a.75.75 0 01-.53-1.28l.621-.622a2.25 2.25 0 00.659-1.59V18h-3a3 3 0 01-3-3V5.25zm1.5 0v9.75c0 .83.67 1.5 1.5 1.5h13.5c.83 0 1.5-.67 1.5-1.5V5.25c0-.83-.67-1.5-1.5-1.5H5.25c-.83 0-1.5.67-1.5 1.5z" clipRule="evenodd" />
                    </svg>
                  </div>
                  System Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-3">
                  <div className="bg-gray-50 p-4 rounded-lg hover:shadow-sm transition-all duration-300">
                    <h4 className="text-sm font-medium text-gray-500 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M13 7H7v6h6V7z" />
                        <path fillRule="evenodd" d="M7 2a1 1 0 012 0v1h2V2a1 1 0 112 0v1h2a2 2 0 012 2v2h1a1 1 0 110 2h-1v2h1a1 1 0 110 2h-1v2a2 2 0 01-2 2h-2v1a1 1 0 11-2 0v-1H9v1a1 1 0 11-2 0v-1H5a2 2 0 01-2-2v-2H2a1 1 0 110-2h1V9H2a1 1 0 010-2h1V5a2 2 0 012-2h2V2zM5 5h10v10H5V5z" clipRule="evenodd" />
                      </svg>
                      CPU
                    </h4>
                    <dl className="mt-2 text-sm text-gray-900 space-y-2">
                      <div className="flex justify-between py-1 border-b border-gray-100">
                        <dt className="font-medium">Cores</dt>
                        <dd className="font-semibold">{healthData.system.cpu.cores}</dd>
                      </div>
                      <div className="flex justify-between py-1">
                        <dt className="font-medium">Load Average (1m, 5m, 15m)</dt>
                        <dd className="font-semibold">{healthData.system.cpu.load.map(l => l.toFixed(2)).join(', ')}</dd>
                      </div>
                    </dl>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg hover:shadow-sm transition-all duration-300">
                    <h4 className="text-sm font-medium text-gray-500 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M5.5 16a3.5 3.5 0 01-.369-6.98 4 4 0 117.753-1.977A4.5 4.5 0 1113.5 16h-8z" />
                      </svg>
                      Memory
                    </h4>
                    <dl className="mt-2 text-sm text-gray-900 space-y-2">
                      <div className="flex justify-between py-1 border-b border-gray-100">
                        <dt className="font-medium">Total</dt>
                        <dd className="font-semibold">{formatBytes(healthData.system.memory.total)}</dd>
                      </div>
                      <div className="flex justify-between py-1 border-b border-gray-100">
                        <dt className="font-medium">Used</dt>
                        <dd className="font-semibold">{formatBytes(healthData.system.memory.used)}</dd>
                      </div>
                      <div className="flex justify-between py-1 border-b border-gray-100">
                        <dt className="font-medium">Free</dt>
                        <dd className="font-semibold">{formatBytes(healthData.system.memory.free)}</dd>
                      </div>
                      <div className="flex justify-between py-1">
                        <dt className="font-medium">Usage</dt>
                        <dd className="font-semibold">
                          {((healthData.system.memory.used / healthData.system.memory.total) * 100).toFixed(2)}%
                        </dd>
                      </div>
                    </dl>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg hover:shadow-sm transition-all duration-300">
                    <h4 className="text-sm font-medium text-gray-500 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
                      </svg>
                      Memory Usage
                    </h4>
                    <div className="mt-4 h-4 bg-gray-200 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-indigo-600 transition-all duration-500"
                        style={{
                          width: `${(healthData.system.memory.used / healthData.system.memory.total) * 100}%`
                        }}
                      ></div>
                    </div>
                    <div className="mt-2 text-xs text-gray-500 flex justify-between">
                      <span>0%</span>
                      <span>50%</span>
                      <span>100%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      ) : null}
    </div>
  );
}
