/**
 * SMTP Mode Comparison Component
 *
 * Displays a comparison table between Auto and Custom test modes
 */

'use client';

import React from 'react';

export default function SmtpModeComparison() {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 mb-4">
        <div className="w-6 h-6 rounded-lg bg-[#66b077]/10 flex items-center justify-center">
          <svg className="w-4 h-4 text-[#66b077]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2h2a2 2 0 002-2z" />
          </svg>
        </div>
        <h3 className="text-lg font-semibold" style={{ color: '#1b130e' }}>Test Mode Comparison</h3>
      </div>
      <p className="text-[#4a3728] mb-4 text-sm">
        Compare features between Auto and Custom modes to choose the best option for your testing needs.
      </p>

      <div className="overflow-x-auto bg-gradient-to-r from-[#66b077]/5 to-[#07880e]/5 border border-[#66b077]/20 rounded-xl p-4">
        <table className="w-full text-sm">
          <thead>
            <tr className="border-b border-[#4a3728]/20">
              <th className="text-left py-3 px-2" style={{ color: '#1b130e' }}>Feature</th>
              <th className="text-center py-3 px-2" style={{ color: '#1b130e' }}>
                <div className="flex flex-col items-center">
                  <span className="font-semibold">Auto Mode</span>
                  <span className="text-xs font-normal" style={{ color: '#66b077' }}>Recommended</span>
                </div>
              </th>
              <th className="text-center py-3 px-2" style={{ color: '#1b130e' }}>Custom Mode</th>
            </tr>
          </thead>
          <tbody className="space-y-1">
            <tr className="border-b border-[#4a3728]/10">
              <td className="py-2 px-2" style={{ color: '#4a3728' }}>SMTP Connectivity Testing</td>
              <td className="text-center py-2 px-2">
                <svg className="w-5 h-5 mx-auto" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </td>
              <td className="text-center py-2 px-2">
                <svg className="w-5 h-5 mx-auto" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </td>
            </tr>
            <tr className="border-b border-[#4a3728]/10">
              <td className="py-2 px-2" style={{ color: '#4a3728' }}>Email Authentication Analysis</td>
              <td className="text-center py-2 px-2">
                <div className="flex flex-col items-center">
                  <svg className="w-5 h-5" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="text-xs mt-1" style={{ color: '#66b077' }}>5-Protocol Analysis</span>
                </div>
              </td>
              <td className="text-center py-2 px-2">
                <svg className="w-5 h-5 mx-auto" style={{ color: '#956b50' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </td>
            </tr>
            <tr className="border-b border-[#4a3728]/10">
              <td className="py-2 px-2" style={{ color: '#4a3728' }}>Custom Recipient Address</td>
              <td className="text-center py-2 px-2">
                <svg className="w-5 h-5 mx-auto" style={{ color: '#956b50' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </td>
              <td className="text-center py-2 px-2">
                <svg className="w-5 h-5 mx-auto" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </td>
            </tr>
            <tr className="border-b border-[#4a3728]/10">
              <td className="py-2 px-2" style={{ color: '#4a3728' }}>Deliverability Recommendations</td>
              <td className="text-center py-2 px-2">
                <svg className="w-5 h-5 mx-auto" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </td>
              <td className="text-center py-2 px-2">
                <svg className="w-5 h-5 mx-auto" style={{ color: '#956b50' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </td>
            </tr>
            <tr className="border-b border-[#4a3728]/10">
              <td className="py-2 px-2" style={{ color: '#4a3728' }}>Diagnostic Logs</td>
              <td className="text-center py-2 px-2">
                <svg className="w-5 h-5 mx-auto" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </td>
              <td className="text-center py-2 px-2">
                <svg className="w-5 h-5 mx-auto" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </td>
            </tr>
            <tr>
              <td className="py-2 px-2 font-medium" style={{ color: '#4a3728' }}>Typical Test Duration</td>
              <td className="text-center py-2 px-2">
                <span className="text-sm font-medium" style={{ color: '#4a3728' }}>10-30s</span>
              </td>
              <td className="text-center py-2 px-2">
                <span className="text-sm font-medium" style={{ color: '#4a3728' }}>5-15s</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Mode Descriptions */}
      <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-3">
        <div className="bg-white/60 backdrop-blur-sm border border-[#66b077]/20 rounded-xl p-4">
          <div className="flex items-start space-x-3">
            <div className="w-2 h-2 bg-[#66b077] rounded-full mt-2 flex-shrink-0"></div>
            <div className="flex-1 min-w-0">
              <h4 className="font-semibold text-[#1b130e] mb-1">Auto Mode</h4>
              <p className="text-sm text-[#4a3728]">
                System generated email with 5-protocol authentication analysis
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white/60 backdrop-blur-sm border border-[#66b077]/20 rounded-xl p-4">
          <div className="flex items-start space-x-3">
            <div className="w-2 h-2 bg-[#66b077] rounded-full mt-2 flex-shrink-0"></div>
            <div className="flex-1 min-w-0">
              <h4 className="font-semibold text-[#1b130e] mb-1">Custom Mode</h4>
              <p className="text-sm text-[#4a3728]">
                Custom recipient with SMTP connectivity test only
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
