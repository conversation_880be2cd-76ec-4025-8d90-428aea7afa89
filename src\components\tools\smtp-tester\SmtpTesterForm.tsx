/**
 * SMTP Tester Form Component
 *
 * Main form component for SMTP server configuration and testing
 */

'use client';

import React, { useState, useEffect } from 'react';
import { SmtpFormData, SmtpValidationErrors, SmtpProvider } from '@/types/smtp';
import { SMTP_PROVIDERS, ENCRYPTION_OPTIONS } from '@/lib/tools/smtp-tester/constants';
import { getDefaultPortForEncryption } from '@/lib/tools/smtp-tester/utils';
import SmtpModeComparison from './SmtpModeComparison';

interface SmtpTesterFormProps {
  formData: SmtpFormData;
  validationErrors: SmtpValidationErrors;
  isLoading: boolean;
  validationSuccess?: boolean;
  onFormDataChange: (updates: Partial<SmtpFormData>) => void;
  onSubmit: (formData: SmtpFormData) => void;
  onValidate?: (formData: SmtpFormData) => void;
}

export default function SmtpTesterForm({
  formData,
  validationErrors,
  isLoading,
  validationSuccess,
  onFormDataChange,
  onSubmit,
  onValidate
}: SmtpTesterFormProps) {
  const [selectedProvider, setSelectedProvider] = useState<string>('custom');
  const [showPassword, setShowPassword] = useState(false);

  // Handle provider selection
  const handleProviderChange = (providerName: string) => {
    setSelectedProvider(providerName);

    const provider = SMTP_PROVIDERS.find(p => p.name === providerName);
    if (provider && provider.name !== 'custom') {
      onFormDataChange({
        server: provider.server,
        port: provider.port.toString(),
        encryption: provider.encryption
      });
    }
  };

  // Handle encryption change and auto-update port
  const handleEncryptionChange = (encryption: 'none' | 'tls' | 'ssl') => {
    const defaultPort = getDefaultPortForEncryption(encryption);
    onFormDataChange({
      encryption,
      port: defaultPort.toString()
    });
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  // Handle validation
  const handleValidate = () => {
    if (onValidate) {
      onValidate(formData);
    }
  };

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-lg border border-[#4a3728]/20 p-6">
      {/* Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-[#66b077] to-[#07880e] flex items-center justify-center">
          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </div>
        <div>
          <h2 className="text-2xl font-bold" style={{ color: '#1b130e' }}>SMTP Configuration & Testing</h2>
          <p className="text-sm" style={{ color: '#4a3728' }}>Configure your SMTP server settings and compare test modes</p>
        </div>
      </div>

      {/* Two-column layout */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
        {/* Left Column - SMTP Configuration Form */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            <div className="w-6 h-6 rounded-lg bg-[#66b077]/10 flex items-center justify-center">
              <svg className="w-4 h-4 text-[#66b077]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold" style={{ color: '#1b130e' }}>Server Configuration</h3>
          </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Provider Selection */}
        <div>
          <label className="block text-sm font-medium mb-2" style={{ color: '#1b130e' }}>
            Email Provider
          </label>
          <select
            value={selectedProvider}
            onChange={(e) => handleProviderChange(e.target.value)}
            className="w-full px-3 py-2 border border-[#4a3728]/30 rounded-md focus:outline-none focus:ring-2 focus:ring-[#66b077] focus:border-transparent"
            style={{ backgroundColor: '#fbfaf8' }}
          >
            {SMTP_PROVIDERS.map((provider) => (
              <option key={provider.name} value={provider.name}>
                {provider.displayName}
              </option>
            ))}
          </select>
        </div>

        {/* SMTP Server */}
        <div>
          <label className="block text-sm font-medium mb-2" style={{ color: '#1b130e' }}>
            SMTP Server *
          </label>
          <input
            type="text"
            value={formData.server}
            onChange={(e) => onFormDataChange({ server: e.target.value })}
            placeholder="e.g., smtp.gmail.com"
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#66b077] focus:border-transparent ${
              validationErrors.server ? 'border-red-500' : 'border-[#4a3728]/30'
            }`}
            style={{ backgroundColor: '#fbfaf8' }}
            disabled={isLoading}
          />
          {validationErrors.server && (
            <p className="text-red-600 text-sm mt-1">{validationErrors.server}</p>
          )}
        </div>

        {/* Port and Encryption */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2" style={{ color: '#1b130e' }}>
              Port *
            </label>
            <input
              type="number"
              value={formData.port}
              onChange={(e) => onFormDataChange({ port: e.target.value })}
              placeholder="587"
              min="1"
              max="65535"
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#66b077] focus:border-transparent ${
                validationErrors.port ? 'border-red-500' : 'border-[#4a3728]/30'
              }`}
              style={{ backgroundColor: '#fbfaf8' }}
              disabled={isLoading}
            />
            {validationErrors.port && (
              <p className="text-red-600 text-sm mt-1">{validationErrors.port}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2" style={{ color: '#1b130e' }}>
              Encryption *
            </label>
            <select
              value={formData.encryption}
              onChange={(e) => handleEncryptionChange(e.target.value as 'none' | 'tls' | 'ssl')}
              className="w-full px-3 py-2 border border-[#4a3728]/30 rounded-md focus:outline-none focus:ring-2 focus:ring-[#66b077] focus:border-transparent"
              style={{ backgroundColor: '#fbfaf8' }}
              disabled={isLoading}
            >
              {ENCRYPTION_OPTIONS.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Username */}
        <div>
          <label className="block text-sm font-medium mb-2" style={{ color: '#1b130e' }}>
            Username *
          </label>
          <input
            type="text"
            value={formData.username}
            onChange={(e) => onFormDataChange({ username: e.target.value })}
            placeholder="<EMAIL>"
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#66b077] focus:border-transparent ${
              validationErrors.username ? 'border-red-500' : 'border-[#4a3728]/30'
            }`}
            style={{ backgroundColor: '#fbfaf8' }}
            disabled={isLoading}
          />
          {validationErrors.username && (
            <p className="text-red-600 text-sm mt-1">{validationErrors.username}</p>
          )}
        </div>

        {/* Password */}
        <div>
          <label className="block text-sm font-medium mb-2" style={{ color: '#1b130e' }}>
            Password *
          </label>
          <div className="relative">
            <input
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={(e) => onFormDataChange({ password: e.target.value })}
              placeholder="App Password or regular password"
              className={`w-full px-3 py-2 pr-10 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#66b077] focus:border-transparent ${
                validationErrors.password ? 'border-red-500' : 'border-[#4a3728]/30'
              }`}
              style={{ backgroundColor: '#fbfaf8' }}
              disabled={isLoading}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
              style={{ color: '#4a3728' }}
            >
              {showPassword ? (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              )}
            </button>
          </div>
          {validationErrors.password && (
            <p className="text-red-600 text-sm mt-1">{validationErrors.password}</p>
          )}
        </div>

        {/* Sender Email */}
        <div>
          <label className="block text-sm font-medium mb-2" style={{ color: '#1b130e' }}>
            Sender Email *
          </label>
          <input
            type="email"
            value={formData.sender}
            onChange={(e) => onFormDataChange({ sender: e.target.value })}
            placeholder="<EMAIL>"
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#66b077] focus:border-transparent ${
              validationErrors.sender ? 'border-red-500' : 'border-[#4a3728]/30'
            }`}
            style={{ backgroundColor: '#fbfaf8' }}
            disabled={isLoading}
          />
          {validationErrors.sender && (
            <p className="text-red-600 text-sm mt-1">{validationErrors.sender}</p>
          )}
        </div>

        {/* Test Mode Selection */}
        <div>
          <label className="block text-sm font-semibold text-[#1b130e] mb-4">
            Test Mode *
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            <label className={`relative flex items-center py-2.5 px-4 border-2 rounded-xl cursor-pointer transition-all duration-200 bg-transparent ${
              formData.testMode === 'auto'
                ? 'border-[#66b077] shadow-md'
                : 'border-[#4a3728]/20 hover:border-[#66b077]/50 hover:shadow-sm'
            } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}>
              <input
                type="radio"
                name="testMode"
                value="auto"
                checked={formData.testMode === 'auto'}
                onChange={(e) => onFormDataChange({ testMode: e.target.value as 'auto' | 'custom' })}
                disabled={isLoading}
                className="sr-only"
              />
              <div className="flex items-center space-x-3 w-full">
                <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                  formData.testMode === 'auto'
                    ? 'border-[#66b077] bg-[#66b077]'
                    : 'border-[#4a3728]/40'
                }`}>
                  {formData.testMode === 'auto' && (
                    <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-0.5">
                    <span className="font-semibold text-[#1b130e] text-sm">Auto Mode</span>
                    <span className="px-1.5 py-0.5 border border-[#66b077]/30 text-[#66b077] bg-transparent text-xs font-medium rounded-full">
                      Recommended
                    </span>
                  </div>
                  <p className="text-xs text-[#4a3728]">
                    Email generation + 5-protocol authentication analysis
                  </p>
                </div>
              </div>
            </label>

            <label className={`relative flex items-center py-2.5 px-4 border-2 rounded-xl cursor-pointer transition-all duration-200 bg-transparent ${
              formData.testMode === 'custom'
                ? 'border-[#66b077] shadow-md'
                : 'border-[#4a3728]/20 hover:border-[#66b077]/50 hover:shadow-sm'
            } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}>
              <input
                type="radio"
                name="testMode"
                value="custom"
                checked={formData.testMode === 'custom'}
                onChange={(e) => onFormDataChange({ testMode: e.target.value as 'auto' | 'custom' })}
                disabled={isLoading}
                className="sr-only"
              />
              <div className="flex items-center space-x-3 w-full">
                <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                  formData.testMode === 'custom'
                    ? 'border-[#66b077] bg-[#66b077]'
                    : 'border-[#4a3728]/40'
                }`}>
                  {formData.testMode === 'custom' && (
                    <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-0.5">
                    <span className="font-semibold text-[#1b130e] text-sm">Custom Mode</span>
                    <span className="px-1.5 py-0.5 border border-[#ce601c]/30 text-[#ce601c] bg-transparent text-xs font-medium rounded-full">
                      SMTP Only
                    </span>
                  </div>
                  <p className="text-xs text-[#4a3728]">
                    Custom recipient • No authentication analysis
                  </p>
                </div>
              </div>
            </label>
          </div>
        </div>

        {/* Custom Recipient (only shown in custom mode) */}
        {formData.testMode === 'custom' && (
          <div>
            <label className="block text-sm font-medium mb-2" style={{ color: '#1b130e' }}>
              Recipient Email *
            </label>
            <input
              type="email"
              value={formData.recipient}
              onChange={(e) => onFormDataChange({ recipient: e.target.value })}
              placeholder="<EMAIL>"
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#66b077] focus:border-transparent ${
                validationErrors.recipient ? 'border-red-500' : 'border-[#4a3728]/30'
              }`}
              style={{ backgroundColor: '#fbfaf8' }}
              disabled={isLoading}
            />
            {validationErrors.recipient && (
              <p className="text-red-600 text-sm mt-1">{validationErrors.recipient}</p>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4">
          {onValidate && (
            <button
              type="button"
              onClick={handleValidate}
              disabled={isLoading}
              className="px-4 py-2 border border-[#4a3728] text-[#4a3728] rounded-md hover:bg-[#4a3728] hover:text-white transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Validate Configuration
            </button>
          )}

          <button
            type="submit"
            disabled={isLoading}
            className="border border-[#ce601c] text-[#ce601c] bg-transparent py-3 px-6 rounded-xl hover:bg-[#ce601c] hover:text-white hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center font-semibold text-base"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-current mr-3"></div>
                Testing...
              </>
            ) : (
              <>
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {formData.testMode === 'auto' ? 'Test with Analysis' : 'Test Connection'}
              </>
            )}
          </button>
        </div>

        {/* Validation Success Feedback */}
        {validationSuccess && (
          <div className="mt-4 p-4 rounded-lg border border-[#66b077]/30 bg-[#66b077]/5">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <svg className="w-6 h-6 text-[#66b077]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="flex-1">
                <h4 className="text-sm font-semibold text-[#66b077] mb-1">
                  SMTP Configuration Validated Successfully
                </h4>
                <p className="text-sm text-[#4a3728]">
                  Your SMTP settings are correct and the connection is working. You can now proceed to test email delivery.
                </p>
              </div>
            </div>
          </div>
        )}
      </form>
        </div>

        {/* Right Column - Mode Comparison with Visual Separator */}
        <div className="relative">
          {/* Vertical Separator (hidden on mobile) */}
          <div className="hidden xl:block absolute left-0 top-0 bottom-0 w-px bg-gradient-to-b from-transparent via-[#4a3728]/20 to-transparent"></div>

          {/* Mode Comparison Content */}
          <div className="xl:pl-8">
            <SmtpModeComparison />
          </div>
        </div>
      </div>
    </div>
  );
}
