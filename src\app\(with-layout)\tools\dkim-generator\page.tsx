'use client';

import { Metadata } from 'next';
import React, { useState, useEffect, useRef } from 'react';
import { generateSessionId } from '@/lib/utils/uuid';
import { useDkimGenerator } from '@/hooks/useDkimGenerator';
import { DkimFormData } from '@/types/dkim';
import DkimGeneratorForm from '@/components/tools/dkim-generator/DkimGeneratorForm';
import DkimResults from '@/components/tools/dkim-generator/DkimResults';
import Breadcrumbs from '@/components/seo/Breadcrumbs';
import { SoftwareApplicationSchema } from '@/components/seo/StructuredData';

// Note: Metadata export moved to metadata.ts file for client components
export default function DkimGeneratorPage() {
  const { generateKeys, validateDns, result, validation, loading, error, clearError } = useDkimGenerator();
  const [validating, setValidating] = useState(false);
  const resultsRef = useRef<HTMLDivElement>(null);

  const handleGenerate = async (formData: DkimFormData) => {
    clearError();

    // Generate session ID for tracking (browser-compatible)
    const sessionId = generateSessionId();

    await generateKeys({
      domain: formData.domain,
      selector: formData.selector,
      keyStrength: formData.keyStrength,
      sessionId,
    });
  };

  const handleValidate = async (domain: string, selector: string, publicKey: string) => {
    setValidating(true);
    try {
      await validateDns(domain, selector, publicKey);
    } finally {
      setValidating(false);
    }
  };

  // Smooth scroll to results when they appear
  useEffect(() => {
    if (result && resultsRef.current) {
      setTimeout(() => {
        resultsRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });
      }, 100);
    }
  }, [result]);

  // Breadcrumb items
  const breadcrumbItems = [
    { name: 'Home', url: '/' },
    { name: 'Tools', url: '/tools' },
    { name: 'DKIM Generator', url: '/tools/dkim-generator', current: true },
  ];

  return (
    <div className="min-h-screen bg-[#fbfaf8]">
        <div className="container mx-auto px-4 py-8 max-w-5xl">
          {/* Breadcrumbs */}
          <div className="mb-6">
            <Breadcrumbs items={breadcrumbItems} />
          </div>

          {/* Modern Header with Enhanced Visual Hierarchy */}
          <div className="mb-10">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-[#66b077] to-[#07880e] rounded-2xl mb-6 shadow-lg">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                </svg>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-[#1b130e] mb-4 tracking-tight">
                DKIM Generator
              </h1>
              <p className="text-xl text-[#4a3728] max-w-2xl mx-auto leading-relaxed">
                Generate secure RSA key pairs and DNS records for DKIM email authentication with enterprise-grade security
              </p>
            </div>

            {/* Feature Highlights */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
              <div className="bg-white/60 backdrop-blur-sm border border-[#4a3728]/20 rounded-xl p-4 text-center">
                <div className="w-10 h-10 bg-[#66b077]/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <svg className="w-5 h-5 text-[#66b077]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-[#1b130e] text-sm">Secure Generation</h3>
                <p className="text-xs text-[#4a3728] mt-1">2048-bit RSA encryption</p>
              </div>
              <div className="bg-white/60 backdrop-blur-sm border border-[#4a3728]/20 rounded-xl p-4 text-center">
                <div className="w-10 h-10 bg-[#66b077]/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <svg className="w-5 h-5 text-[#66b077]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-[#1b130e] text-sm">DNS Validation</h3>
                <p className="text-xs text-[#4a3728] mt-1">Real-time verification</p>
              </div>
              <div className="bg-white/60 backdrop-blur-sm border border-[#4a3728]/20 rounded-xl p-4 text-center">
                <div className="w-10 h-10 bg-[#66b077]/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <svg className="w-5 h-5 text-[#66b077]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-[#1b130e] text-sm">Instant Setup</h3>
                <p className="text-xs text-[#4a3728] mt-1">Ready-to-use records</p>
              </div>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <svg className="h-5 w-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <span className="text-red-700 font-medium">Error:</span>
                <span className="text-red-600 ml-2">{error}</span>
              </div>
              <button
                onClick={clearError}
                className="mt-2 text-red-600 hover:text-red-800 text-sm underline"
              >
                Dismiss
              </button>
            </div>
          )}

          {/* Validation Results */}
          {validation && (
            <div className={`mb-6 border rounded-lg p-4 ${
              validation.isValid
                ? 'bg-[#66b077] bg-opacity-10 border-[#66b077]'
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-center mb-2">
                <svg
                  className={`h-5 w-5 mr-2 ${validation.isValid ? 'text-[#66b077]' : 'text-red-500'}`}
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  {validation.isValid ? (
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  ) : (
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  )}
                </svg>
                <span className={`font-medium ${validation.isValid ? 'text-[#1b130e]' : 'text-red-700'}`}>
                  DNS Validation {validation.isValid ? 'Successful' : 'Failed'}
                </span>
              </div>

              {validation.exists && validation.records.length > 0 && (
                <div className="mt-3">
                  <p className="text-sm font-medium text-[#1b130e] mb-1">Found DNS Records:</p>
                  {validation.records.map((record, index) => (
                    <div key={index} className="bg-white bg-opacity-50 p-2 rounded text-xs font-mono break-all">
                      {record}
                    </div>
                  ))}
                </div>
              )}

              {validation.errors.length > 0 && (
                <div className="mt-3">
                  <p className="text-sm font-medium text-red-700 mb-1">Issues Found:</p>
                  <ul className="text-sm text-red-600 space-y-1">
                    {validation.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}

              <p className="text-xs text-gray-600 mt-2">
                Last checked: {new Date(validation.lastChecked).toLocaleString()}
              </p>
            </div>
          )}

          {/* Main Content */}
          <div className="space-y-8">
            {/* Form */}
            <DkimGeneratorForm
              onSubmit={handleGenerate}
              loading={loading}
            />

            {/* Results */}
            {result && (
              <div ref={resultsRef}>
                <DkimResults
                  result={result}
                  loading={loading}
                  onValidate={handleValidate}
                  validating={validating}
                />
              </div>
            )}
          </div>

          {/* Information Section */}
          <div className="mt-12 bg-white/80 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-6 shadow-sm transition-all duration-300 hover:bg-white/90 hover:shadow-md">
            <h2 className="text-xl font-semibold text-[#1b130e] mb-4">
              About DKIM Authentication
            </h2>
            <div className="prose prose-sm text-[#4a3728] max-w-none">
              <p className="mb-4">
                <strong>DomainKeys Identified Mail (DKIM)</strong> is an email authentication method
                that allows the receiver to check that an email claimed to have come from a specific
                domain was indeed authorized by the owner of that domain.
              </p>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-[#1b130e] mb-2">How DKIM Works:</h3>
                  <ul className="space-y-1 text-sm">
                    <li>• Email server signs outgoing messages with a private key</li>
                    <li>• Public key is published in DNS as a TXT record</li>
                    <li>• Receiving servers verify signatures using the public key</li>
                    <li>• Valid signatures prove email authenticity</li>
                  </ul>
                </div>
                <div>
                  <h3 className="font-semibold text-[#1b130e] mb-2">Benefits:</h3>
                  <ul className="space-y-1 text-sm">
                    <li>• Improved email deliverability</li>
                    <li>• Protection against email spoofing</li>
                    <li>• Enhanced sender reputation</li>
                    <li>• Required for DMARC compliance</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
    </div>
  );
}
