'use client';

import { useState, useEffect, useRef } from 'react';
import Navbar from './Navbar';
import EmailList from './EmailList';
import ReactIframeEmailViewer from './ReactIframeEmailViewer';
import SimpleIframeEmailViewer from './SimpleIframeEmailViewer';
import EmailControls from './EmailControls';
import Notifications from './Notifications';
import AdContainer from './AdContainer';
import StickyAdContainer from './StickyAdContainer';
import RendererToggle from './RendererToggle';
import AppLoadingOverlay from './AppLoadingOverlay';
import { useEmailSelection } from '@/hooks/useEmailSelection';
import { useEmailStorage } from '@/hooks/useEmailStorage';
import { useAutoRefresh } from '@/hooks/useAutoRefresh';
import { useSupabaseRealtime } from '@/hooks/useSupabaseRealtime';
import { UI, STORAGE, TIMEOUTS, ADMIN } from '@/lib/constants';
import { ApiError, logError } from '@/lib/errorHandling';
import { Email } from '@/lib/emailProcessing';
// Import PostHog analytics functions
import {
  trackEmailGenerated,
  trackEmailAddressCopied,
  trackPageView,
  trackEvent
} from '@/lib/analytics/posthog';

/**
 * EmailApp Component
 *
 * Main component for the VanishPost application that displays the email interface
 */
export default function EmailApp() {
  // We don't need the mobile view state anymore as it's handled in the EmailList and EmailViewer components

  // State for error and success messages
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // State for button loading states
  const [isGeneratingAddress, setIsGeneratingAddress] = useState<boolean>(false);

  // State for unified loading - controls both inbox and email viewer
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Effect to handle initial loading state with a consistent delay
  useEffect(() => {
    // Set a timeout to ensure components load simultaneously
    const loadingTimer = setTimeout(() => {
      setIsLoading(false);
    }, 800); // 800ms delay for a smooth, consistent appearance

    return () => clearTimeout(loadingTimer);
  }, []);

  // Flag to prevent showing error messages immediately after generating a new address
  const [justGeneratedAddress, setJustGeneratedAddress] = useState<boolean>(false);

  // Flag to completely disable auto-refresh for a period after generating a new address
  const [disableAutoRefresh, setDisableAutoRefresh] = useState<boolean>(false);

  // Flag to indicate we're in an address transition period - blocks all email updates
  const [inAddressTransition, setInAddressTransition] = useState<boolean>(false);

  // Store the previous address to help with filtering during transitions
  const previousAddressRef = useRef<string | null>(null);

  // State for copy button text
  const [copyButtonText, setCopyButtonText] = useState<string>('Copy');

  // State for last refreshed time
  const [lastRefreshed, setLastRefreshed] = useState<string>('');

  // Initialize state for emails first
  const [emails, setEmailsState] = useState<Email[]>([]);

  // Initialize the email selection hook with the emails state
  const emailSelection = useEmailSelection(emails, setEmailsState);

  // Then use both in the email storage hook
  const {
    emailAddress,
    showingGuideEmails,
    setEmailAddress,
    fetchEmails,
    showGuideEmails,
    setShowingGuideEmails,
    setLastDeletionTimestamp
  } = useEmailStorage({
    loadReadStatus: () => emailSelection.loadReadStatus(),
    filterOutDeletedEmails: (emailsList) => emailSelection.filterOutDeletedEmails(emailsList),
    filterDuringAddressTransition: (emailsList, currentAddress, previousAddress) =>
      emailSelection.filterDuringAddressTransition(emailsList, currentAddress, previousAddress),
    setEmails: setEmailsState,
    inAddressTransition,
    previousAddressRef
  });

  // Function to refresh the inbox
  const refreshInbox = async () => {
    // Log the current state for debugging
    console.log(`[${new Date().toISOString()}] refreshInbox called with state:`, {
      disableAutoRefresh,
      showingGuideEmails,
      isGeneratingAddress,
      emailAddress
    });

    // Prevent refresh if auto-refresh is disabled, if showing guide emails, or if generating a new address
    if (disableAutoRefresh || showingGuideEmails || isGeneratingAddress) {
      console.log(`[${new Date().toISOString()}] Skipping refresh because:`,
        disableAutoRefresh ? 'auto-refresh is disabled' :
        showingGuideEmails ? 'showing guide emails' :
        'generating a new address');
      return;
    }

    // Abort any in-flight delete requests to prevent race conditions
    emailSelection.abortDeleteRequests();

    // Track inbox refresh event with PostHog
    trackEvent('inbox_refreshed', { isManual: true })
      .catch(err => {
        console.error('Failed to track inbox refresh:', err);
      });

    // Store the current email address to ensure we're refreshing the correct inbox
    const currentAddress = emailAddress;
    if (!currentAddress) {
      console.log(`[${new Date().toISOString()}] Skipping refresh because no email address is set`);
      showGuideEmails();
      return;
    }

    console.log(`[${new Date().toISOString()}] Starting refresh for address:`, currentAddress);
    setError(null);

    try {
      // Add a minimal delay for UI feedback (300ms)
      await new Promise(resolve => setTimeout(resolve, 300));

      // Check again that the email address hasn't changed during the delay
      if (emailAddress !== currentAddress) {
        console.log(`[${new Date().toISOString()}] Email address changed during refresh delay. Skipping refresh for:`, currentAddress);
        return;
      }

      console.log(`[${new Date().toISOString()}] Refreshing inbox for address:`, currentAddress);
      // Fetch emails from the API
      await fetchEmails(currentAddress);

      // Check again that the email address hasn't changed after fetching
      if (emailAddress !== currentAddress) {
        console.log(`[${new Date().toISOString()}] Email address changed after fetching. Skipping UI update for:`, currentAddress);
        return;
      }

      // Update last refreshed time
      const now = new Date();
      setLastRefreshed(now.toLocaleTimeString([], UI.TIME_FORMAT_WITH_SECONDS));

      // Pulse animation on the inbox header to indicate successful refresh
      const inboxHeader = document.querySelector('.inbox-header');
      if (inboxHeader) {
        inboxHeader.classList.add('animate-pulse-indigo');
        setTimeout(() => {
          inboxHeader.classList.remove('animate-pulse-indigo');
        }, 2000);
      }
    } catch (error) {
      // Check if the email address has changed during the fetch
      if (emailAddress !== currentAddress) {
        console.log(`[${new Date().toISOString()}] Error occurred during refresh, but email address changed. Ignoring error for:`, currentAddress);
        return;
      }

      // Check if this is an ApiError with status 410 (Gone)
      if (error instanceof ApiError && error.status === 410) {
        // This will be handled by the useEmailStorage hook
        console.log(`[${new Date().toISOString()}] Email address has expired. This will be handled by the useEmailStorage hook.`);
      } else {
        logError('refreshing inbox', error);
        setError('Failed to refresh inbox');
      }
    }
  };

  // Use the auto-refresh hook
  const { isRefreshing } = useAutoRefresh({
    emailAddress,
    disableAutoRefresh,
    showingGuideEmails,
    isGeneratingAddress,
    justGeneratedAddress,
    onRefresh: refreshInbox
  });

  // Use the Supabase real-time subscription hook
  const { isSubscribed } = useSupabaseRealtime({
    emailAddress,
    enabled: !disableAutoRefresh && !showingGuideEmails && !isGeneratingAddress && !inAddressTransition,
    onNewEmails: (newEmails) => {
      console.log(`[${new Date().toISOString()}] Received new emails via real-time subscription:`, newEmails.length);

      // If we're in an address transition, don't update the UI
      if (inAddressTransition) {
        console.log(`[${new Date().toISOString()}] In address transition, ignoring real-time update`);
        return;
      }

      // Apply the filter again just to be safe
      const filteredEmails = emailSelection.filterOutDeletedEmails(newEmails);
      if (filteredEmails.length !== newEmails.length) {
        console.log(`[${new Date().toISOString()}] Additional filtering removed ${newEmails.length - filteredEmails.length} emails`);
      }

      setEmailsState(filteredEmails);

      // Update last refreshed time
      const now = new Date();
      setLastRefreshed(now.toLocaleTimeString([], UI.TIME_FORMAT_WITH_SECONDS));

      // Pulse animation on the inbox header to indicate new emails
      const inboxHeader = document.querySelector('.inbox-header');
      if (inboxHeader) {
        inboxHeader.classList.add('animate-pulse-indigo');
        setTimeout(() => {
          inboxHeader.classList.remove('animate-pulse-indigo');
        }, 2000);
      }
    },
    loadReadStatus: () => emailSelection.loadReadStatus(),
    filterOutDeletedEmails: (emailsList) => emailSelection.filterOutDeletedEmails(emailsList),
    filterDuringAddressTransition: (emailsList, currentAddress, previousAddress) =>
      emailSelection.filterDuringAddressTransition(emailsList, currentAddress, previousAddress),
    inAddressTransition,
    previousAddressRef
  });

  // Function to generate a new email address
  const generateNewAddress = async () => {
    if (isGeneratingAddress) return; // Prevent multiple clicks

    // Store the current email address to ensure we don't process emails for it after generating a new one
    const oldEmailAddress = emailAddress;
    console.log(`[${new Date().toISOString()}] Generating new address. Current address:`, oldEmailAddress);

    // Store the current address as the previous address for filtering
    if (oldEmailAddress) {
      previousAddressRef.current = oldEmailAddress;
      console.log(`[${new Date().toISOString()}] Stored previous address: ${oldEmailAddress}`);
    }

    // First, clear the current emails and selected email immediately
    // This must happen BEFORE setting the transition flag to ensure UI is cleared first
    console.log(`[${new Date().toISOString()}] Clearing emails and selected email`);
    setEmailsState([]);
    emailSelection.clearSelectedEmail();

    // Set the address transition flag to block UI updates during transition
    // This will force the EmailList component to show the transition state
    setInAddressTransition(true);
    console.log(`[${new Date().toISOString()}] Set address transition flag to true`);

    // Immediately disable auto-refresh to prevent fetching emails for the old address
    console.log(`[${new Date().toISOString()}] Disabling auto-refresh during address generation`);
    setDisableAutoRefresh(true);
    setIsGeneratingAddress(true);
    setError(null);

    try {
      // Add a small delay to ensure the UI has updated before proceeding
      await new Promise(resolve => setTimeout(resolve, 50));

      // Abort any in-flight delete requests to prevent race conditions
      emailSelection.abortDeleteRequests();

      // Clear the deleted emails set to start fresh with the new address
      emailSelection.clearDeletedEmailsSet();

      // Clear any cached emails from previous addresses
      localStorage.removeItem(STORAGE.READ_STATUS_KEY);

      // Explicitly set showingGuideEmails to false to ensure auto-refresh works
      // Force this synchronous update by using a callback to ensure this value is accessible immediately
      setShowingGuideEmails(false);
      console.log(`[${new Date().toISOString()}] Explicitly setting showingGuideEmails to false`);

      // Set email address to null to abort any in-flight requests
      // This will trigger the abort logic in useEmailStorage
      setEmailAddress(null);

      // Add a small delay to ensure all state updates and aborts have been processed
      await new Promise(resolve => setTimeout(resolve, 100));

      // Call the API to generate a new email address
      console.log(`[${new Date().toISOString()}] Calling API to generate new address...`);
      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      // Add a minimal delay for UI feedback (300ms)
      await new Promise(resolve => setTimeout(resolve, 300));

      const data = await response.json();

      if (data.success) {
        // Store the generated email address and expiration date
        const generatedAddress = data.emailAddress;
        const emailExpirationDate = new Date(data.expirationDate);
        console.log(`[${new Date().toISOString()}] New address generated successfully:`, generatedAddress, 'expires at:', emailExpirationDate);

        // Update the expiration date state and store in localStorage
        setExpirationDate(emailExpirationDate);
        localStorage.setItem(STORAGE.EMAIL_EXPIRATION_KEY, emailExpirationDate.toISOString());
        console.log(`[${new Date().toISOString()}] Stored expiration date in localStorage:`, emailExpirationDate);

        // Update the email address in the UI
        // This will also reset any in-flight fetch requests via the useEmailStorage hook
        setEmailAddress(generatedAddress);
        setIsGeneratingAddress(false);

        // Clear the address transition flag after the new address is set
        console.log(`[${new Date().toISOString()}] Clearing address transition flag`);
        setInAddressTransition(false);

        // Clear the previous address reference after a delay to ensure all filtering is complete
        setTimeout(() => {
          previousAddressRef.current = null;
          console.log(`[${new Date().toISOString()}] Cleared previous address reference after successful address generation`);
        }, 5000); // 5 second delay to ensure all in-flight requests are complete

        // Track email generation event with PostHog
        try {
          trackEmailGenerated(generatedAddress)
            .catch(err => {
              console.error('Failed to track email generation:', err);
            });
        } catch (error) {
          console.error('Unexpected error in email generation tracking:', error);
        }

        // Set the flag that we just generated a new address
        setJustGeneratedAddress(true);

        // Add a delay before enabling auto-refresh and performing the first GET API call
        // This ensures the server has time to register the new address
        const delaySeconds = TIMEOUTS.NEW_ADDRESS_DELAY/1000;
        console.log(`[${new Date().toISOString()}] Adding a ${delaySeconds} second delay before first GET API call for new address:`, generatedAddress);

        // First, verify the address is registered by making a verification call
        // Add a slightly longer delay to ensure the database has time to register the new address
        setTimeout(async () => {
          try {
            console.log(`[${new Date().toISOString()}] Verifying address registration:`, generatedAddress);

            // Add a small delay to ensure the database has time to register the new address
            await new Promise(resolve => setTimeout(resolve, 500));

            const verifyResponse = await fetch(`/api/verify?address=${encodeURIComponent(generatedAddress)}`);
            const verifyData = await verifyResponse.json();

            if (verifyData.success) {
              console.log(`[${new Date().toISOString()}] Address verified successfully:`, generatedAddress);

              // Update expiration date from verification response if available
              if (verifyData.expirationDate) {
                const verifiedExpirationDate = new Date(verifyData.expirationDate);
                setExpirationDate(verifiedExpirationDate);
                localStorage.setItem(STORAGE.EMAIL_EXPIRATION_KEY, verifiedExpirationDate.toISOString());
                console.log(`[${new Date().toISOString()}] Updated expiration date from verification and stored in localStorage:`, verifiedExpirationDate);
              }

              // Reset the justGeneratedAddress flag and enable auto-refresh in sequence
              console.log(`[${new Date().toISOString()}] Resetting justGeneratedAddress flag for:`, generatedAddress);
              setJustGeneratedAddress(false);

              // Enable auto-refresh for the new address and then perform refresh
              // Using a callback to ensure state is updated before refresh
              console.log(`[${new Date().toISOString()}] Enabling auto-refresh for new address:`, generatedAddress);
              setDisableAutoRefresh(false);

              // Add a small delay to ensure state updates are processed
              setTimeout(() => {
                // Perform a manual refresh
                console.log(`[${new Date().toISOString()}] Performing first GET API call for new address:`, generatedAddress);
                refreshInbox();
              }, 50);

              // Schedule a follow-up refresh to ensure we get emails
              setTimeout(() => {
                if (emailAddress === generatedAddress) {
                  console.log(`[${new Date().toISOString()}] Performing follow-up refresh for:`, generatedAddress);
                  refreshInbox();
                }
              }, 5000); // 5 seconds after the initial refresh
            } else {
              console.log(`[${new Date().toISOString()}] Address verification failed, retrying in 2 seconds:`, generatedAddress);

              // Retry after 3 seconds - give the database more time to register the address
              setTimeout(async () => {
                try {
                  console.log(`[${new Date().toISOString()}] Retrying address verification:`, generatedAddress);
                  const retryResponse = await fetch(`/api/verify?address=${encodeURIComponent(generatedAddress)}`);
                  const retryData = await retryResponse.json();

                  if (retryData.success) {
                    console.log(`[${new Date().toISOString()}] Address verified successfully on retry:`, generatedAddress);

                    // Update expiration date from retry verification response if available
                    if (retryData.expirationDate) {
                      const verifiedExpirationDate = new Date(retryData.expirationDate);
                      setExpirationDate(verifiedExpirationDate);
                      localStorage.setItem(STORAGE.EMAIL_EXPIRATION_KEY, verifiedExpirationDate.toISOString());
                      console.log(`[${new Date().toISOString()}] Updated expiration date from retry verification and stored in localStorage:`, verifiedExpirationDate);
                    }

                    // Reset the justGeneratedAddress flag
                    setJustGeneratedAddress(false);

                    // Enable auto-refresh for the new address
                    console.log(`[${new Date().toISOString()}] Enabling auto-refresh for new address after retry:`, generatedAddress);
                    setDisableAutoRefresh(false);

                    // Add a small delay to ensure state updates are processed
                    setTimeout(() => {
                      // Perform a manual refresh
                      console.log(`[${new Date().toISOString()}] Performing first GET API call after retry for:`, generatedAddress);
                      refreshInbox();
                    }, 50);
                  } else {
                    console.log(`[${new Date().toISOString()}] Address verification failed after retry, enabling auto-refresh anyway:`, generatedAddress);

                    // Enable auto-refresh anyway as a fallback
                    setJustGeneratedAddress(false);
                    setDisableAutoRefresh(false);

                    // Add a small delay to ensure state updates are processed
                    setTimeout(() => {
                      // Perform a manual refresh
                      console.log(`[${new Date().toISOString()}] Performing first GET API call after fallback for:`, generatedAddress);
                      refreshInbox();
                    }, 50);
                  }
                } catch (verifyError) {
                  console.log(`[${new Date().toISOString()}] Error during address verification retry, enabling auto-refresh anyway:`, verifyError);

                  // Enable auto-refresh anyway as a fallback
                  setJustGeneratedAddress(false);
                  setDisableAutoRefresh(false);

                  // Add a small delay to ensure state updates are processed
                  setTimeout(() => {
                    // Perform a manual refresh
                    console.log(`[${new Date().toISOString()}] Performing first GET API call after error fallback for:`, generatedAddress);
                    refreshInbox();
                  }, 50);
                }
              }, 3000);
            }
          } catch (verifyError) {
            console.log(`[${new Date().toISOString()}] Error during address verification, will enable auto-refresh as fallback:`, verifyError);

            // Enable auto-refresh anyway as a fallback after a delay
            setTimeout(() => {
              console.log(`[${new Date().toISOString()}] Enabling auto-refresh as fallback after verification error:`, generatedAddress);
              setJustGeneratedAddress(false);
              setDisableAutoRefresh(false);

              // Add a small delay to ensure state updates are processed
              setTimeout(() => {
                // Perform a manual refresh
                console.log(`[${new Date().toISOString()}] Performing first GET API call after verification error:`, generatedAddress);
                refreshInbox();
              }, 50);
            }, 3000);
          }
        }, TIMEOUTS.NEW_ADDRESS_DELAY);

        // Show a success notification
        setSuccessMessage('New email address generated successfully!');

        // Remove the notification after 3 seconds
        setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
      } else {
        throw new Error(data.message || 'Failed to generate email address');
      }
    } catch (error) {
      logError('generating new address', error);

      // Check if we need to initialize the database
      const errorMessage = error instanceof Error ? error.message : String(error);

      if (errorMessage.includes('emailExpirationMinutes')) {
        console.error('Configuration error detected. Attempting to initialize configuration...');

        try {
          // Use the PUBLIC_PATH constant from ADMIN
          const adminPath = ADMIN.PUBLIC_PATH;

          // Try to initialize the configuration using the public admin path
          const initResponse = await fetch(`/api/${adminPath}/init-direct`);
          const initData = await initResponse.json();

          if (initData.success) {
            console.log('Configuration initialized successfully. Retrying...');
            // Try again after a short delay
            setTimeout(() => {
              generateNewAddress();
            }, 1000);
            return;
          } else {
            console.error('Failed to initialize configuration:', initData);
            setError('Failed to generate a new email address. Configuration error.');
          }
        } catch (initError) {
          console.error('Error initializing configuration:', initError);
          setError('Failed to generate a new email address. Configuration error.');
        }
      } else {
        setError('Failed to generate a new email address');
      }

      setIsGeneratingAddress(false);

      // Clear the address transition flag in case of error
      console.log(`[${new Date().toISOString()}] Clearing address transition flag due to error`);
      setInAddressTransition(false);

      // Also clear the previous address reference to prevent filtering
      previousAddressRef.current = null;
      console.log(`[${new Date().toISOString()}] Cleared previous address reference due to error`);
    }
  };

  // Function to copy the email address to clipboard
  const copyEmailAddress = () => {
    if (typeof navigator !== 'undefined' && emailAddress) {
      navigator.clipboard.writeText(emailAddress)
        .then(() => {
          // Show a temporary success message
          setCopyButtonText('Copied!');
          setTimeout(() => {
            setCopyButtonText('Copy');
          }, 2000);

          // Track email address copied event with PostHog
          try {
            trackEmailAddressCopied(emailAddress)
              .catch(err => {
                console.error('Failed to track email copy:', err);
              });
          } catch (error) {
            console.error('Unexpected error in email copy tracking:', error);
          }
        })
        .catch((error) => {
          logError('copying email address', error);
          setError('Failed to copy email address');
        });
    }
  };

  // Function to format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  // State for mobile view
  const [showMobileInbox, setShowMobileInbox] = useState<boolean>(true);

  // State for email expiration date
  const [expirationDate, setExpirationDate] = useState<Date | null>(null);

  // State for email renderer type - default to 'react-iframe' for SSR
  const [rendererType, setRendererType] = useState<'react-iframe' | 'simple-iframe'>('react-iframe');

  // State for zoom scale - default to 100%
  const [currentScale, setCurrentScale] = useState<number>(100);

  // Effect to load renderer preference from localStorage after hydration
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedPreference = localStorage.getItem('emailRendererPreference');
      // Only consider 'simple-iframe' as an alternative, as we've removed react-letter
      if (storedPreference === 'simple-iframe') {
        setRendererType('simple-iframe');
      } else {
        // For any other value (including old 'react-letter' values), use 'react-iframe'
        if (storedPreference !== 'react-iframe') {
          // Update the preference to react-iframe if it was something else
          localStorage.setItem('emailRendererPreference', 'react-iframe');
        }
      }
    }
  }, []);

  // Load expiration date from localStorage on mount
  useEffect(() => {
    try {
      const storedExpirationDate = localStorage.getItem(STORAGE.EMAIL_EXPIRATION_KEY);
      if (storedExpirationDate) {
        setExpirationDate(new Date(storedExpirationDate));
        console.log(`[${new Date().toISOString()}] Loaded expiration date from localStorage:`, new Date(storedExpirationDate));
      }
    } catch (error) {
      console.error('Error loading expiration date from localStorage:', error);
    }
  }, []);

  // Function to handle back to inbox (mobile only)
  const handleBackToInbox = () => {
    setShowMobileInbox(true);
  };

  // Function to handle email selection (including mobile view)
  const handleSelectEmail = (emailId: string, isDirectClick?: boolean) => {
    emailSelection.selectEmail(emailId);

    // Check if this is a guide email
    const isGuideEmail = emailId.startsWith('guide-');

    // On mobile, hide the inbox and show the email content in these cases:
    // 1. If it's a direct click from the user (clicking an email card or "View Email" button)
    // 2. If it's a regular email (not a guide email) being auto-selected
    if (isDirectClick || (!isGuideEmail && !isDirectClick)) {
      setShowMobileInbox(false);
    }

    // Track email viewed event with PostHog
    try {
      trackEvent('email_viewed', { emailId })
        .catch(err => {
          console.error('Failed to track email view:', err);
        });
    } catch (error) {
      console.error('Unexpected error in email view tracking:', error);
    }
  };

  // Effect to set loading state to false after initial load
  useEffect(() => {
    // Set loading to false after a short delay
    const timer = setTimeout(() => {
      setIsLoading(false);

      // Initial refresh of emails if we have an address
      if (emailAddress && emails.length === 0 && !disableAutoRefresh) {
        refreshInbox();
      }
    }, 1000);

    return () => clearTimeout(timer);
  }, [emailAddress, emails.length, disableAutoRefresh]);

  // Effect to track home page view on initial load only
  useEffect(() => {
    // Track the home page view manually (since we excluded it from middleware)
    console.log('Tracking home page view');
    // Use window.location.pathname to get the actual path, or default to '/'
    const currentPath = typeof window !== 'undefined' ? window.location.pathname : '/';

    // Track page view with PostHog
    try {
      trackPageView(currentPath)
        .catch(err => {
          console.error('Failed to track home page view:', err);
        });
    } catch (error) {
      console.error('Unexpected error in page view tracking:', error);
    }

    // This effect should only run once on component mount
  }, []);

  // Effect to save renderer preference when it changes
  useEffect(() => {
    // Only run this effect on the client side
    if (typeof window === 'undefined') return;

    // Use setTimeout to ensure this runs after hydration
    const timeoutId = setTimeout(() => {
      localStorage.setItem('emailRendererPreference', rendererType);

      // Track renderer change event with PostHog
      try {
        trackEvent('renderer_changed', {
          renderer: rendererType
        })
        .catch(err => {
          console.error('Failed to track renderer change:', err);
        });
      } catch (error) {
        console.error('Unexpected error in renderer change tracking:', error);
      }
    }, 0);

    return () => clearTimeout(timeoutId);
  }, [rendererType]);

  // Effect to load zoom level from localStorage after hydration
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedZoomLevel = localStorage.getItem('emailZoomLevel');
      if (storedZoomLevel) {
        const parsedZoomLevel = parseInt(storedZoomLevel, 10);
        if (!isNaN(parsedZoomLevel) && parsedZoomLevel >= 50 && parsedZoomLevel <= 200) {
          setCurrentScale(parsedZoomLevel);
        }
      }
    }
  }, []);

  // Effect to save zoom level when it changes
  useEffect(() => {
    // Only run this effect on the client side
    if (typeof window === 'undefined') return;

    // Use setTimeout to ensure this runs after hydration
    const timeoutId = setTimeout(() => {
      localStorage.setItem('emailZoomLevel', currentScale.toString());
    }, 0);

    return () => clearTimeout(timeoutId);
  }, [currentScale]);

  // Function to handle zoom in
  const handleZoomIn = () => {
    setCurrentScale(prev => Math.min(200, prev + 10));
  };

  // Function to handle zoom out
  const handleZoomOut = () => {
    setCurrentScale(prev => Math.max(50, prev - 10));
  };

  // Function to reset zoom
  const handleResetZoom = () => {
    setCurrentScale(100);
  };

  // Effect to listen for email expired events
  useEffect(() => {
    const handleEmailExpired = (event: Event) => {
      const customEvent = event as CustomEvent;
      const address = customEvent.detail?.address;

      // Clear the selected email
      emailSelection.clearSelectedEmail();

      // Show error message
      setError(`Email address ${address} has expired. Please generate a new address.`);

      // Note: The guide emails will be shown by the useEmailStorage hook,
      // and the first guide email will be auto-selected by the guide-emails-shown event handler
    };

    window.addEventListener('email-expired', handleEmailExpired);

    return () => {
      window.removeEventListener('email-expired', handleEmailExpired);
    };
  }, [emailSelection]);

  // Effect to listen for guide emails shown events
  useEffect(() => {
    const handleGuideEmailsShown = (event: Event) => {
      const customEvent = event as CustomEvent;
      const firstEmailId = customEvent.detail?.firstEmailId;

      // Auto-select the first guide email
      if (firstEmailId) {
        console.log(`Auto-selecting first guide email: ${firstEmailId}`);
        // Add a small delay to ensure the emails are properly loaded
        setTimeout(() => {
          // Select the email but keep the inbox visible on mobile
          emailSelection.selectEmail(firstEmailId);

          // Make sure the inbox is visible on mobile when guide emails are shown
          setShowMobileInbox(true);

          // Track email viewed event with PostHog
          try {
            trackEvent('email_viewed', { emailId: firstEmailId })
              .catch(err => {
                console.error('Failed to track guide email view:', err);
              });
          } catch (error) {
            console.error('Unexpected error in guide email view tracking:', error);
          }
        }, 50);
      }
    };

    window.addEventListener('guide-emails-shown', handleGuideEmailsShown);

    return () => {
      window.removeEventListener('guide-emails-shown', handleGuideEmailsShown);
    };
  }, [emailSelection]);

  return (
    <div className="flex flex-col min-h-screen relative" style={{ backgroundColor: 'var(--earth-beige-light)' }}>
      {/* Application-wide loading overlay */}
      <AppLoadingOverlay isVisible={isLoading} />

      {/* Left Rail Ad Container - Sticky to the left side of the screen */}
      <div className="hidden xl:block">
        <StickyAdContainer
          placementId="left-rail"
          className="mx-2"
          position="left"
          style={{
            maxHeight: '90vh'
          }}
        />
      </div>

      {/* Right Rail Ad Container - Sticky to the right side of the screen */}
      <div className="hidden xl:block">
        <StickyAdContainer
          placementId="right-rail"
          className="mx-2"
          position="right"
          style={{
            maxHeight: '90vh'
          }}
        />
      </div>

      {/* Navbar */}
      <Navbar />

      {/* Top Ad Container - Between Navbar and Email Controls */}
      <AdContainer
        placementId="top"
        className="w-full max-w-5xl mx-auto my-2 px-4 sm:px-6"
        style={{ minHeight: '90px', maxHeight: '100px' }}
      />

      {/* Email Controls Section */}
      <EmailControls
        emailAddress={emailAddress || ''}
        isGeneratingAddress={isGeneratingAddress}
        onGenerateNewAddress={generateNewAddress}
        onCopyEmailAddress={copyEmailAddress}
        copyButtonText={copyButtonText}
      />

      {/* Mobile Navigation Indicator */}
      <div className="md:hidden flex justify-center mt-4 mb-2">
        <div className="flex space-x-3">
          <button
            onClick={() => setShowMobileInbox(true)}
            className={`px-5 py-2.5 rounded-full text-sm font-medium transition-all duration-300 ${
              showMobileInbox
                ? 'bg-gradient-to-r from-[#ce601c] to-[#e67e22] text-white shadow-md transform scale-105'
                : 'bg-white text-neutral-700 hover:bg-neutral-50'
            }`}
          >
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6z" />
                <path d="M22 6l-10 7L2 6" />
              </svg>
              Inbox
            </div>
          </button>
          <button
            onClick={() => setShowMobileInbox(false)}
            className={`px-5 py-2.5 rounded-full text-sm font-medium transition-all duration-300 ${
              !showMobileInbox && emailSelection.selectedEmail
                ? 'bg-gradient-to-r from-[#ce601c] to-[#e67e22] text-white shadow-md transform scale-105'
                : 'bg-white text-neutral-700 hover:bg-neutral-50'
            }`}
            disabled={!emailSelection.selectedEmail}
          >
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
              </svg>
              View Email
            </div>
          </button>
        </div>
      </div>

      {/* Removed Renderer Toggle from here - moved to bottom of email viewer */}

      {/* Middle Ad Container - Between Email Controls and Inbox/Viewer */}
      <AdContainer
        placementId="middle"
        className="w-full max-w-5xl mx-auto my-2 px-4 sm:px-6"
        style={{ minHeight: '90px', maxHeight: '100px' }}
      />

      {/* Main Content - Responsive Layout */}
      <div className="flex flex-col md:flex-row flex-1 overflow-hidden max-w-5xl w-full mx-auto mt-2 md:mt-4 mb-8 rounded-lg shadow-md"
           style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)', transform: 'translateZ(0)' }}>
        {/* Email List */}
        <EmailList
          emails={emails}
          selectedEmailId={emailSelection.selectedEmail?.id || null}
          isLoading={false} // Remove individual loading state
          isRefreshing={isRefreshing}
          lastRefreshed={lastRefreshed}
          isRealtimeActive={isSubscribed}
          disableAutoRefresh={disableAutoRefresh || showingGuideEmails}
          isGeneratingAddress={isGeneratingAddress}
          expirationDate={showingGuideEmails ? null : expirationDate}
          showingGuideEmails={showingGuideEmails}
          inAddressTransition={inAddressTransition}
          onSelectEmail={handleSelectEmail}
          onDeleteEmail={(emailId, e) => {
            console.log(`[${new Date().toISOString()}] User initiated deletion of email ID ${emailId}`);

            // Set the last deletion timestamp before deleting the email
            // This will abort any in-flight GET requests
            setLastDeletionTimestamp();

            // Track email deletion event with PostHog
            trackEvent('email_deleted', { emailId }).catch(err => console.error('Failed to track email deletion:', err));

            // Delete the email - this will now use the queue system to handle concurrent deletions
            emailSelection.deleteEmail(emailId, e as React.MouseEvent);
          }}
          onRefreshInbox={refreshInbox}
          showMobileInbox={showMobileInbox}
        />

        {/* Email Viewer - Conditionally using the selected renderer */}
        {rendererType === 'simple-iframe' ? (
          <SimpleIframeEmailViewer
            selectedEmail={emailSelection.selectedEmail}
            emailAddress={emailAddress || ''}
            isLoading={false} // Remove individual loading state
            formatFileSize={formatFileSize}
            onBackToInbox={handleBackToInbox}
            showMobileInbox={showMobileInbox}
            inAddressTransition={inAddressTransition}
          />
        ) : (
          <ReactIframeEmailViewer
            selectedEmail={emailSelection.selectedEmail}
            emailAddress={emailAddress || ''}
            isLoading={false} // Remove individual loading state
            formatFileSize={formatFileSize}
            onBackToInbox={handleBackToInbox}
            showMobileInbox={showMobileInbox}
            scaleFactor={currentScale} // Pass the current scale factor
            inAddressTransition={inAddressTransition}
          />
        )}
      </div>

      {/* Notifications */}
      <Notifications
        error={error}
        successMessage={successMessage}
        onClearError={() => setError(null)}
        onClearSuccess={() => setSuccessMessage(null)}
      />

      {/* Renderer Toggle with Zoom Controls - positioned at the bottom middle */}
      <RendererToggle
        rendererType={rendererType}
        setRendererType={setRendererType}
        currentScale={currentScale}
        onZoomIn={handleZoomIn}
        onZoomOut={handleZoomOut}
        onResetZoom={handleResetZoom}
        showZoomControls={!!emailSelection.selectedEmail && rendererType === 'react-iframe'}
      />
    </div>
  );
}
