/**
 * Custom hook for managing email storage
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Email, createGuideEmails, mapApiEmailToUiEmail } from '@/lib/emailProcessing';
import { STORAGE, GUI<PERSON> } from '@/lib/constants';
import { ApiError, logError } from '@/lib/errorHandling';

interface EmailStorageOptions {
  /** Function to load read status from localStorage */
  loadReadStatus: () => Record<string, boolean>;

  /** Function to filter out deleted emails from a list */
  filterOutDeletedEmails: (emails: Email[]) => Email[];

  /** Function to filter out emails from a previous address during transition */
  filterDuringAddressTransition: (emails: Email[], currentAddress: string | null, previousAddress: string | null) => Email[];

  /** External function to set emails */
  setEmails: React.Dispatch<React.SetStateAction<Email[]>>;

  /** Flag indicating we're in an address transition period */
  inAddressTransition: boolean;

  /** Reference to the previous address */
  previousAddressRef: React.RefObject<string | null>;
}

interface EmailStorageResult {
  /** Current email address */
  emailAddress: string | null;

  /** Whether guide emails are being shown */
  showingGuideEmails: boolean;

  /** Function to set the email address */
  setEmailAddress: (address: string | null) => void;

  /** Function to fetch emails from the API */
  fetchEmails: (address: string) => Promise<void>;

  /** Function to show guide emails */
  showGuideEmails: () => void;

  /** Function to set whether guide emails are being shown */
  setShowingGuideEmails: React.Dispatch<React.SetStateAction<boolean>>;

  /** Function to set the last deletion timestamp */
  setLastDeletionTimestamp: () => void;
}

/**
 * Custom hook for managing email storage
 */
export const useEmailStorage = ({
  loadReadStatus,
  filterOutDeletedEmails,
  filterDuringAddressTransition,
  setEmails: externalSetEmails,
  inAddressTransition,
  previousAddressRef
}: EmailStorageOptions): EmailStorageResult => {
  // State for the email address
  const [emailAddress, setEmailAddressState] = useState<string | null>(null);

  // Use the external setEmails function
  const setEmails = externalSetEmails;

  // Get the current emails from the external state
  const emails = [];

  // State for whether guide emails are being shown
  const [showingGuideEmails, setShowingGuideEmails] = useState(true);

  // Load email address from localStorage on mount and check if it has expired
  useEffect(() => {
    try {
      const storedAddress = localStorage.getItem(STORAGE.EMAIL_ADDRESS_KEY);
      const storedExpirationDate = localStorage.getItem(STORAGE.EMAIL_EXPIRATION_KEY);

      if (storedAddress && storedExpirationDate) {
        // Check if the email address has expired
        const expirationDate = new Date(storedExpirationDate);
        const now = new Date();

        if (expirationDate > now) {
          // Email address is still valid
          console.log(`Email address ${storedAddress} is still valid, expires at: ${expirationDate.toISOString()}`);
          setEmailAddressState(storedAddress);
          setShowingGuideEmails(false);
        } else {
          // Email address has expired
          console.warn(`Email address ${storedAddress} has expired at ${expirationDate.toISOString()}, current time: ${now.toISOString()}`);

          // Clear the expired email address and expiration date from localStorage
          localStorage.removeItem(STORAGE.EMAIL_ADDRESS_KEY);
          localStorage.removeItem(STORAGE.EMAIL_EXPIRATION_KEY);

          // Show guide emails
          showGuideEmails();

          // Show a notification to the user (using a custom event)
          const expiredEvent = new CustomEvent('email-expired', {
            detail: { address: storedAddress }
          });
          window.dispatchEvent(expiredEvent);
        }
      } else if (storedAddress) {
        // We have an address but no expiration date (shouldn't happen, but just in case)
        console.warn(`Email address ${storedAddress} found but no expiration date, treating as valid`);
        setEmailAddressState(storedAddress);
        setShowingGuideEmails(false);
      } else {
        // Show guide emails if no stored address
        showGuideEmails();
      }
    } catch (error) {
      logError('loading email address from localStorage', error);
      // Show guide emails in case of error
      showGuideEmails();
    }
  }, []);

  // Track the current fetch request address to handle race conditions
  const currentFetchRef = useRef<string | null>(null);

  // Track the abort controller for the current fetch request
  const abortControllerRef = useRef<AbortController | null>(null);

  // Track the last deletion timestamp to discard GET requests that were initiated before a deletion
  const lastDeletionTimestampRef = useRef<number | null>(null);

  // Function to set the email address
  const setEmailAddress = useCallback((address: string | null): void => {
    console.log(`[${new Date().toISOString()}] Setting email address to:`, address);

    // First, clear any existing emails to prevent showing emails from previous addresses
    setEmails([]);

    // Abort any in-flight requests
    if (abortControllerRef.current) {
      console.log(`[${new Date().toISOString()}] Aborting in-flight request for:`, currentFetchRef.current);
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
      currentFetchRef.current = null;
    }

    // Reset the last deletion timestamp to prevent race conditions
    lastDeletionTimestampRef.current = null;
    console.log(`[${new Date().toISOString()}] Reset last deletion timestamp`);

    // Then update the email address state
    setEmailAddressState(address);

    try {
      if (address) {
        // Clear the read status to prevent showing emails from previous addresses
        localStorage.removeItem(STORAGE.READ_STATUS_KEY);

        // Save the new address
        localStorage.setItem(STORAGE.EMAIL_ADDRESS_KEY, address);
        console.log(`[${new Date().toISOString()}] Email address set to:`, address);
      } else {
        // Clear email address and expiration date from localStorage
        localStorage.removeItem(STORAGE.EMAIL_ADDRESS_KEY);
        localStorage.removeItem(STORAGE.EMAIL_EXPIRATION_KEY);
        console.log(`[${new Date().toISOString()}] Email address and expiration date cleared`);
      }
    } catch (error) {
      logError('saving email address to localStorage', error);
    }
  }, [setEmails]);

  // Function to fetch emails from the API
  const fetchEmails = useCallback(async (address: string): Promise<void> => {
    try {
      // Verify we're fetching for the current email address
      if (address !== emailAddress && emailAddress !== null) {
        console.warn(`[${new Date().toISOString()}] Attempted to fetch emails for a different address than the current one.`,
          { requestedAddress: address, currentAddress: emailAddress });
        return;
      }

      // Abort any existing request before starting a new one
      if (abortControllerRef.current) {
        console.log(`[${new Date().toISOString()}] Aborting previous request before starting new one for:`, address);
        abortControllerRef.current.abort();
      }

      // Create a new abort controller for this request
      abortControllerRef.current = new AbortController();
      const signal = abortControllerRef.current.signal;

      // Set this as the current fetch request
      currentFetchRef.current = address;

      // Record the fetch start time to compare with deletion timestamps
      const fetchStartTime = Date.now();

      console.log(`[${new Date().toISOString()}] Fetching emails for: ${address}`);

      // Check if a deletion occurred after this fetch was initiated
      if (lastDeletionTimestampRef.current !== null && lastDeletionTimestampRef.current > fetchStartTime) {
        console.warn(`[${new Date().toISOString()}] A deletion occurred before this fetch started. Skipping fetch for:`, address);
        return;
      }

      // Double-check that the email address hasn't changed before making the request
      if (address !== emailAddress && emailAddress !== null) {
        console.warn(`[${new Date().toISOString()}] Email address changed before making request. Aborting fetch for:`, address);
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
          abortControllerRef.current = null;
        }
        currentFetchRef.current = null;
        return;
      }

      const response = await fetch(`/api/emails/${encodeURIComponent(address)}`, {
        signal // Pass the abort signal to the fetch request
      });

      // After the fetch completes, check if this is still the current request
      // If not, discard the results
      if (currentFetchRef.current !== address) {
        console.warn(`[${new Date().toISOString()}] A newer fetch request was initiated. Discarding results for:`, address);
        return;
      }

      // Check if the email address has changed during the fetch
      if (address !== emailAddress && emailAddress !== null) {
        console.warn(`[${new Date().toISOString()}] Email address changed during fetch. Discarding results for:`, address);
        return;
      }

      // Check if a deletion occurred after this fetch was initiated
      // If so, discard the results to prevent showing deleted emails
      if (lastDeletionTimestampRef.current !== null && lastDeletionTimestampRef.current > fetchStartTime) {
        console.warn(`[${new Date().toISOString()}] A deletion occurred after this fetch was initiated. Discarding results for:`, address);
        return;
      }

      if (!response.ok) {
        // Special handling for 410 Gone (expired email address)
        if (response.status === 410) {
          console.warn(`Email address ${address} has expired. Clearing address and showing guide emails.`);
          // Clear the expired email address and expiration date from localStorage
          localStorage.removeItem(STORAGE.EMAIL_ADDRESS_KEY);
          localStorage.removeItem(STORAGE.EMAIL_EXPIRATION_KEY);
          // Clear the email address state
          setEmailAddressState(null);
          // Show guide emails
          showGuideEmails();

          // Show a notification to the user (using a custom event)
          const expiredEvent = new CustomEvent('email-expired', {
            detail: { address }
          });
          window.dispatchEvent(expiredEvent);

          return;
        }

        // Special handling for 404 Not Found (email address not found)
        if (response.status === 404) {
          console.warn(`Email address ${address} not found. Returning empty emails.`);
          setEmails([]);
          setShowingGuideEmails(false);
          return;
        }

        throw new ApiError(
          `API error: ${response.status} ${response.statusText}`,
          response.status
        );
      }

      const data = await response.json();

      // Check again after parsing JSON
      if (currentFetchRef.current !== address) {
        console.warn(`[${new Date().toISOString()}] A newer fetch request was initiated while parsing JSON. Discarding results for:`, address);
        return;
      }

      // Check if the email address has changed during JSON parsing
      if (address !== emailAddress && emailAddress !== null) {
        console.warn(`[${new Date().toISOString()}] Email address changed during JSON parsing. Discarding results for:`, address);
        return;
      }

      // Check again if a deletion occurred after this fetch was initiated
      if (lastDeletionTimestampRef.current !== null && lastDeletionTimestampRef.current > fetchStartTime) {
        console.warn(`[${new Date().toISOString()}] A deletion occurred while parsing JSON. Discarding results for:`, address);
        return;
      }

      if (data && data.success) {
        // Triple-check that the email address hasn't changed during the fetch
        if (address !== emailAddress && emailAddress !== null) {
          console.warn(`[${new Date().toISOString()}] Email address changed during fetch. Discarding results.`,
            { fetchAddress: address, currentAddress: emailAddress });
          return;
        }

        // Get read status from localStorage
        const readStatusMap = loadReadStatus();

        // Map the API response to match the Email interface
        const mappedEmails = data.emails.map((apiEmail: any) =>
          mapApiEmailToUiEmail(apiEmail, readStatusMap)
        );

        // Final check before updating UI
        if (currentFetchRef.current !== address) {
          console.warn(`[${new Date().toISOString()}] A newer fetch request completed before updating UI. Discarding results for:`, address);
          return;
        }

        // Final check if the email address has changed before updating UI
        if (address !== emailAddress && emailAddress !== null) {
          console.warn(`[${new Date().toISOString()}] Email address changed before updating UI. Discarding results for:`, address);
          return;
        }

        // Final check if a deletion occurred after this fetch was initiated
        if (lastDeletionTimestampRef.current !== null && lastDeletionTimestampRef.current > fetchStartTime) {
          console.warn(`[${new Date().toISOString()}] A deletion occurred before updating UI. Discarding results for:`, address);
          return;
        }

        console.log(`[${new Date().toISOString()}] Emails fetched:`, mappedEmails.length, 'for address:', address);

        // First, filter out any deleted emails
        let filteredEmails = filterOutDeletedEmails(mappedEmails);
        console.log(`[${new Date().toISOString()}] Filtered out ${mappedEmails.length - filteredEmails.length} deleted emails`);

        // If we're in an address transition, apply additional filtering
        if (inAddressTransition && previousAddressRef.current) {
          console.log(`[${new Date().toISOString()}] In address transition, applying additional filtering`);
          console.log(`[${new Date().toISOString()}] Current address: ${address}, Previous address: ${previousAddressRef.current}`);

          // Apply the address transition filter
          filteredEmails = filterDuringAddressTransition(
            filteredEmails,
            address,
            previousAddressRef.current
          );
        }

        // If we're in an address transition and this is for the previous address, don't update the UI
        if (inAddressTransition && previousAddressRef.current && address === previousAddressRef.current) {
          console.log(`[${new Date().toISOString()}] Discarding emails for previous address during transition`);
          return;
        }

        setEmails(filteredEmails);
        setShowingGuideEmails(false);
      } else if (data) {
        throw new ApiError(
          data.message || 'Failed to fetch emails',
          response.status
        );
      }
    } catch (error) {
      // Check if the request was aborted
      if (error instanceof DOMException && error.name === 'AbortError') {
        console.log(`[${new Date().toISOString()}] Fetch request was aborted for:`, address);
        return; // Don't propagate abort errors
      }

      // Check if the email address has changed during the fetch
      if (address !== emailAddress && emailAddress !== null) {
        console.warn(`[${new Date().toISOString()}] Email address changed during error handling. Ignoring error for:`, address);
        return;
      }

      // Only log and propagate error if this is still the current request
      if (currentFetchRef.current === address) {
        logError('fetching emails', error);
        throw error;
      } else {
        console.warn(`[${new Date().toISOString()}] Error occurred for outdated fetch request. Ignoring:`, address);
      }
    } finally {
      // If this is the current request that just completed, clear the abort controller
      if (currentFetchRef.current === address) {
        abortControllerRef.current = null;
        console.log(`[${new Date().toISOString()}] Cleared abort controller for:`, address);
      }
    }
  }, [loadReadStatus, emailAddress, filterOutDeletedEmails, filterDuringAddressTransition, inAddressTransition, previousAddressRef]);

  // Function to set the last deletion timestamp
  const setLastDeletionTimestamp = useCallback((): void => {
    // Abort any in-flight requests to prevent race conditions
    if (abortControllerRef.current) {
      console.log(`[${new Date().toISOString()}] Aborting in-flight request during deletion for:`, currentFetchRef.current);
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    // Set the timestamp
    lastDeletionTimestampRef.current = Date.now();
    console.log(`[${new Date().toISOString()}] Set last deletion timestamp: ${lastDeletionTimestampRef.current}`);

    // Clear the current fetch reference to prevent stale data from being displayed
    currentFetchRef.current = null;
  }, []);

  // Function to show guide emails
  const showGuideEmails = useCallback((): void => {
    // Create guide emails
    const guideEmails = createGuideEmails();

    // Set the emails and update the showingGuideEmails state
    setEmails(guideEmails);
    setShowingGuideEmails(true);

    // Dispatch a custom event to notify that guide emails are shown
    // This will be used to auto-select the first guide email
    if (guideEmails.length > 0) {
      // Add a small delay to ensure the emails are properly set in state
      setTimeout(() => {
        console.log('Dispatching guide-emails-shown event with first email ID:', guideEmails[0].id);
        const guideEmailsEvent = new CustomEvent('guide-emails-shown', {
          detail: { firstEmailId: guideEmails[0].id }
        });
        window.dispatchEvent(guideEmailsEvent);
      }, 50);
    }
  }, []);

  return {
    emailAddress,
    showingGuideEmails,
    setEmailAddress,
    fetchEmails,
    showGuideEmails,
    setShowingGuideEmails,
    setLastDeletionTimestamp
  };
};
