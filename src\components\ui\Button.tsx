'use client';

import React from 'react';
import type { ComponentPropsWithoutRef, ElementType, ReactElement } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

// Define button variants using class-variance-authority
const buttonVariants = cva(
  // Base styles applied to all buttons
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        primary: "bg-[#ce601c] text-white hover:bg-[#b85518] focus-visible:ring-[#ce601c]",
        secondary: "bg-white text-[#605f5f] border border-[#605f5f] hover:bg-neutral-50 focus-visible:ring-[#605f5f]",
        danger: "bg-[#f15757] text-white hover:bg-[#e13a3a] focus-visible:ring-[#f15757]",
        success: "bg-[#28c08e] text-white hover:bg-[#1fa678] focus-visible:ring-[#28c08e]",
        ghost: "bg-transparent text-neutral-700 hover:bg-neutral-100 hover:text-neutral-800 focus-visible:ring-neutral-500",
        link: "bg-transparent text-[#ce601c] hover:text-[#b85518] underline-offset-4 hover:underline p-0 h-auto focus-visible:ring-[#ce601c]",
      },
      size: {
        xs: "h-7 px-2 text-xs",
        sm: "h-8 px-3 text-xs",
        md: "h-9 px-4 text-sm",
        lg: "h-10 px-5 text-base",
        icon: "h-9 w-9",
      },
      fullWidth: {
        true: "w-full",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "md",
      fullWidth: false,
    },
  }
);

type ButtonBaseProps = {
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
} & VariantProps<typeof buttonVariants>;

export type ButtonProps<C extends ElementType = 'button'> = ButtonBaseProps & {
  as?: C;
} & Omit<ComponentPropsWithoutRef<C>, keyof ButtonBaseProps>;

const Button = React.forwardRef<HTMLElement, ButtonProps<ElementType>>(
  ({ as, className, variant, size, fullWidth, isLoading, leftIcon, rightIcon, children, ...props }, ref) => {
    const Comp = as || 'button';
    const isButtonElement = Comp === 'button';
    const buttonProps = isButtonElement ? { disabled: isLoading || props.disabled } : {};

    // Use createElement instead of JSX for dynamic components
    return React.createElement(
      Comp,
      {
        className: cn(buttonVariants({ variant, size, fullWidth, className })),
        ref,
        ...buttonProps,
        ...props
      },
      // Create an array of children elements
      [
        isLoading && React.createElement(
          'svg',
          {
            key: 'loading-spinner',
            className: "animate-spin -ml-1 mr-2 h-4 w-4",
            xmlns: "http://www.w3.org/2000/svg",
            fill: "none",
            viewBox: "0 0 24 24"
          },
          [
            React.createElement('circle', {
              key: 'circle',
              className: "opacity-25",
              cx: "12",
              cy: "12",
              r: "10",
              stroke: "currentColor",
              strokeWidth: "4"
            }),
            React.createElement('path', {
              key: 'path',
              className: "opacity-75",
              fill: "currentColor",
              d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            })
          ]
        ),
        !isLoading && leftIcon && React.createElement('span', { key: 'left-icon', className: "mr-2" }, leftIcon),
        children,
        !isLoading && rightIcon && React.createElement('span', { key: 'right-icon', className: "ml-2" }, rightIcon)
      ].filter(Boolean)
    );
  }
);

Button.displayName = "Button";

export { Button, buttonVariants };
