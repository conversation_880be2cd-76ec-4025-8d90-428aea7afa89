'use client';

import { useState, useEffect } from 'react';
import { AdConfig, AdDisplayOptions } from '@/lib/config/types';
import { Input } from '@/components/ui/Input';
import { Select } from '@/components/ui/Select';
import { Textarea } from '@/components/ui/Textarea';
import { Button } from '@/components/ui/Button';
import { Checkbox } from '@/components/ui/Checkbox';

interface AdPlacementFormProps {
  onSubmit: (
    placementId: string,
    domain: string,
    adUnitId: string,
    adClientId: string,
    isEnabled: boolean,
    deviceTypes: string[],
    displayOptions?: AdDisplayOptions
  ) => Promise<void>;
  onCancel: () => void;
  initialValues?: AdConfig;
  isEditMode?: boolean;
  domains?: string[];
}

export default function AdPlacementForm({
  onSubmit,
  onCancel,
  initialValues,
  isEditMode = false,
  domains = []
}: AdPlacementFormProps) {
  const [placementId, setPlacementId] = useState(initialValues?.placementId || '');
  const [domain, setDomain] = useState(initialValues?.domain || (domains.length > 0 ? domains[0] : ''));
  const [adUnitId, setAdUnitId] = useState(initialValues?.adUnitId || '');
  const [adClientId, setAdClientId] = useState(initialValues?.adClientId || 'ca-pub-8397529755029714');
  const [isEnabled, setIsEnabled] = useState(initialValues?.isEnabled ?? true);
  const [deviceTypes, setDeviceTypes] = useState<string[]>(initialValues?.deviceTypes || ['desktop', 'tablet', 'mobile']);
  const [displayOptionsJson, setDisplayOptionsJson] = useState(
    initialValues?.displayOptions ? JSON.stringify(initialValues.displayOptions, null, 2) : ''
  );
  const [jsonError, setJsonError] = useState<string | null>(null);
  const [customDomain, setCustomDomain] = useState('');
  const [isCustomDomain, setIsCustomDomain] = useState(false);

  // Update domain when domains prop changes
  useEffect(() => {
    if (!initialValues && domains.length > 0 && !domain) {
      setDomain(domains[0]);
    }
  }, [domains, domain, initialValues]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!placementId.trim()) {
      alert('Placement ID is required');
      return;
    }

    if (!domain.trim()) {
      alert('Domain is required');
      return;
    }

    if (!adUnitId.trim()) {
      alert('Ad Unit ID is required');
      return;
    }

    if (!adClientId.trim()) {
      alert('Ad Client ID is required');
      return;
    }

    // Validate Ad Client ID format (should be ca-pub-XXXXXXXXXXXXXXXX)
    const adClientIdRegex = /^ca-pub-\d{16}$/;
    if (!adClientIdRegex.test(adClientId)) {
      alert('Ad Client ID must be in the format ca-pub-XXXXXXXXXXXXXXXX (16 digits)');
      return;
    }

    if (deviceTypes.length === 0) {
      alert('At least one device type must be selected');
      return;
    }

    // Parse display options
    let displayOptions: AdDisplayOptions | undefined;

    if (displayOptionsJson.trim()) {
      try {
        displayOptions = JSON.parse(displayOptionsJson);
        setJsonError(null);
      } catch (error) {
        setJsonError('Invalid JSON format');
        return;
      }
    } else {
      // Explicitly set displayOptions to an empty object when the JSON is empty
      // This ensures that empty display options are properly sent to the server
      displayOptions = {};
    }

    // Submit form
    await onSubmit(
      placementId,
      isCustomDomain ? customDomain : domain,
      adUnitId,
      adClientId,
      isEnabled,
      deviceTypes,
      displayOptions
    );
  };

  // Handle device type toggle
  const toggleDeviceType = (type: string) => {
    if (deviceTypes.includes(type)) {
      setDeviceTypes(deviceTypes.filter(t => t !== type));
    } else {
      setDeviceTypes([...deviceTypes, type]);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
        <div className="sm:col-span-3">
          <label htmlFor="placement-id" className="block text-sm font-medium text-gray-700 mb-1">
            Placement ID
          </label>
          <Input
            id="placement-id"
            value={placementId}
            onChange={(e) => setPlacementId(e.target.value)}
            disabled={isEditMode}
            required
          />
          <p className="mt-2 text-sm text-gray-500">
            A unique identifier for this ad placement
          </p>
        </div>

        <div className="sm:col-span-3">
          <label htmlFor="domain" className="block text-sm font-medium text-gray-700 mb-1">
            Domain
          </label>
          {isEditMode ? (
            <Input
              id="domain"
              value={domain}
              disabled
            />
          ) : (
            <>
              {isCustomDomain ? (
                <Input
                  id="custom-domain"
                  value={customDomain}
                  onChange={(e) => setCustomDomain(e.target.value)}
                  required
                />
              ) : domains.length > 0 ? (
                <Select
                  id="domain"
                  value={domain}
                  onChange={(e) => setDomain(e.target.value)}
                  required
                >
                  {domains.map((d) => (
                    <option key={d} value={d}>
                      {d}
                    </option>
                  ))}
                </Select>
              ) : (
                <Input
                  id="domain"
                  value={domain}
                  onChange={(e) => setDomain(e.target.value)}
                  required
                />
              )}
              {domains.length > 0 && (
                <div className="mt-2">
                  <Checkbox
                    id="custom-domain-toggle"
                    checked={isCustomDomain}
                    onCheckedChange={(checked) => setIsCustomDomain(checked)}
                    label="Use custom domain"
                  />
                </div>
              )}
            </>
          )}
          <p className="mt-2 text-sm text-gray-500">
            The domain this ad placement applies to
          </p>
        </div>

        <div className="sm:col-span-6">
          <label htmlFor="ad-client-id" className="block text-sm font-medium text-gray-700 mb-1">
            Ad Client ID
          </label>
          <Input
            id="ad-client-id"
            value={adClientId}
            onChange={(e) => setAdClientId(e.target.value)}
            required
          />
          <p className="mt-2 text-sm text-gray-500">
            The publisher ID from Google AdSense (format: ca-pub-XXXXXXXXXXXXXXXX, 16 digits)
          </p>
        </div>

        <div className="sm:col-span-6">
          <label htmlFor="ad-unit-id" className="block text-sm font-medium text-gray-700 mb-1">
            Ad Unit ID
          </label>
          <Input
            id="ad-unit-id"
            value={adUnitId}
            onChange={(e) => setAdUnitId(e.target.value)}
            required
          />
          <p className="mt-2 text-sm text-gray-500">
            The ad unit ID from your ad provider (e.g., Google AdSense)
          </p>
        </div>

        <div className="sm:col-span-6">
          <fieldset>
            <legend className="text-sm font-medium text-gray-700 mb-2">Device Types</legend>
            <div className="space-y-2">
              <Checkbox
                id="desktop"
                checked={deviceTypes.includes('desktop')}
                onCheckedChange={() => toggleDeviceType('desktop')}
                label="Desktop"
              />
              <Checkbox
                id="tablet"
                checked={deviceTypes.includes('tablet')}
                onCheckedChange={() => toggleDeviceType('tablet')}
                label="Tablet"
              />
              <Checkbox
                id="mobile"
                checked={deviceTypes.includes('mobile')}
                onCheckedChange={() => toggleDeviceType('mobile')}
                label="Mobile"
              />
            </div>
          </fieldset>
        </div>

        <div className="sm:col-span-6">
          <div className="flex items-center justify-between">
            <label htmlFor="is-enabled" className="text-sm font-medium text-gray-700">
              Enabled
            </label>
            <button
              type="button"
              onClick={() => setIsEnabled(!isEnabled)}
              className={`${
                isEnabled ? 'bg-indigo-600' : 'bg-gray-200'
              } relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500`}
              role="switch"
              aria-checked={isEnabled}
            >
              <span className="sr-only">Enable ad placement</span>
              <span
                className={`${
                  isEnabled ? 'translate-x-5' : 'translate-x-0'
                } pointer-events-none relative inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200`}
              >
                <span
                  className={`${
                    isEnabled ? 'opacity-0 ease-out duration-100' : 'opacity-100 ease-in duration-200'
                  } absolute inset-0 h-full w-full flex items-center justify-center transition-opacity`}
                  aria-hidden="true"
                >
                  <svg className="h-3 w-3 text-gray-400" fill="none" viewBox="0 0 12 12">
                    <path
                      d="M4 8l2-2m0 0l2-2M6 6L4 4m2 2l2 2"
                      stroke="currentColor"
                      strokeWidth={2}
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </span>
                <span
                  className={`${
                    isEnabled ? 'opacity-100 ease-in duration-200' : 'opacity-0 ease-out duration-100'
                  } absolute inset-0 h-full w-full flex items-center justify-center transition-opacity`}
                  aria-hidden="true"
                >
                  <svg className="h-3 w-3 text-indigo-600" fill="currentColor" viewBox="0 0 12 12">
                    <path d="M3.707 5.293a1 1 0 00-1.414 1.414l1.414-1.414zM5 8l-.707.707a1 1 0 001.414 0L5 8zm4.707-3.293a1 1 0 00-1.414-1.414l1.414 1.414zm-7.414 2l2 2 1.414-1.414-2-2-1.414 1.414zm3.414 2l4-4-1.414-1.414-4 4 1.414 1.414z" />
                  </svg>
                </span>
              </span>
            </button>
          </div>
          <p className="mt-2 text-sm text-gray-500">
            Whether this ad placement is currently active
          </p>
        </div>

        <div className="sm:col-span-6">
          <div className="flex justify-between items-center mb-1">
            <label htmlFor="display-options" className="block text-sm font-medium text-gray-700">
              Display Options (JSON)
            </label>
            <button
              type="button"
              onClick={() => setDisplayOptionsJson('')}
              className="text-xs text-indigo-600 hover:text-indigo-800"
            >
              Clear
            </button>
          </div>
          <Textarea
            id="display-options"
            rows={6}
            value={displayOptionsJson}
            onChange={(e) => setDisplayOptionsJson(e.target.value)}
            className="font-mono"
            placeholder='{\n  "delaySeconds": 5,\n  "displayMode": "overlay",\n  "dismissible": true\n}'
            error={jsonError || undefined}
          />
          <p className="mt-2 text-sm text-gray-500">
            Optional JSON configuration for how the ad should be displayed. Clear the field to remove all display options.
          </p>
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <Button
          type="button"
          onClick={onCancel}
          variant="secondary"
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="primary"
        >
          {isEditMode ? 'Update' : 'Create'}
        </Button>
      </div>
    </form>
  );
}
